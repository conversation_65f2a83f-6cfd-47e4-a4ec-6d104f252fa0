<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.center</groupId>
    <artifactId>center-dependencies</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <properties>
        <revision>1.0.1</revision>
        <!--hadoop-->
        <org.apache.hadoop.version>3.4.0</org.apache.hadoop.version>
        <!--    minio-->
        <minio.version>8.5.12</minio.version>
        <!--    统计依赖管理-->
        <spring.boot.version>2.7.18</spring.boot.version>
        <org.springdoc.version>1.6.15</org.springdoc.version>
        <!--    Web相关-->
        <org.springframework.version>5.3.31</org.springframework.version>
        <!--    JWT认证-->
        <io.jsonwebtoken.version>0.9.1</io.jsonwebtoken.version>
        <!--    DB相关-->
        <org.flywaydb.version>7.7.3</org.flywaydb.version>
        <druid.version>1.2.21</druid.version>
        <mybatis.plus.version>3.5.5</mybatis.plus.version>
        <mybatis.plus.generator.version>3.5.5</mybatis.plus.generator.version>
        <dynamic.datasource.version>4.3.0</dynamic.datasource.version>
        <mybatis.plus.join.version>1.4.10</mybatis.plus.join.version>
        <easy.trans.version>2.2.11</easy.trans.version>
        <mysql.version>8.0.33</mysql.version>
        <org.redisson>3.18.0</org.redisson>
        <!-- 工具类相关 -->
        <captcha-plus.version>1.0.10</captcha-plus.version>
        <jsoup.version>1.17.2</jsoup.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <hutool.version>5.8.25</hutool.version>
        <easyexcel.verion>3.3.2</easyexcel.verion>
        <velocity.version>2.3</velocity.version>
        <screw.version>1.0.5</screw.version>
        <fastjson.version>1.2.83</fastjson.version>
        <guava.version>33.0.0-jre</guava.version>
        <guice.version>5.1.0</guice.version>
        <commons-net.version>3.10.0</commons-net.version>
        <jsch.version>0.1.55</jsch.version>
        <tika-core.version>2.9.1</tika-core.version>
        <ip2region.version>2.7.0</ip2region.version>
        <bizlog-sdk.version>3.0.6</bizlog-sdk.version>
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <commons-io.version>2.15.1</commons-io.version>
        <com.querydsl.version>5.0.0</com.querydsl.version>
        <orika.version>1.5.4</orika.version>
        <slf4j.version>1.7.32</slf4j.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <com.fasterxml.jackson.core.version>2.13.5</com.fasterxml.jackson.core.version>
        <postgresql.version>42.4.1</postgresql.version>
        <kafka.version>3.8.0</kafka.version>
        <spring.kafka.version>3.0.0</spring.kafka.version>
        <trino.version>439</trino.version>
        <com.squareup.okhttp>4.11.0</com.squareup.okhttp>
        <quartz.version>2.2.0</quartz.version>
        <!--    Maven相关-->
        <java.version>1.8</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven-surefire-plugin.version>3.0.0-M5</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!--    敏感词过滤-->
        <yidun-java-sdk>1.3.4</yidun-java-sdk>
        <okhttp-sse.version>4.11.0</okhttp-sse.version>
        <!--腾讯语音-->
        <com.tencentcloudapi.version>3.1.1126</com.tencentcloudapi.version>
        <tencentcloud-speech-sdk-java>1.0.49</tencentcloud-speech-sdk-java>
    </properties>


    <dependencyManagement>


        <dependencies>


            <!--hadoop-->
            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-common</artifactId>
                <version>${org.apache.hadoop.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-reload4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-hdfs</artifactId>
                <version>${org.apache.hadoop.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.hadoop</groupId>
                <artifactId>hadoop-client</artifactId>
                <version>${org.apache.hadoop.version}</version>
            </dependency>

            <!-- 统一依赖管理 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${org.springdoc.version}</version>
            </dependency>
            <!--    JWS认证-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${io.jsonwebtoken.version}</version>
            </dependency>

            <!--DB相关-->
            <!--    querydsl-->
            <dependency>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-sql</artifactId>
                <version>${com.querydsl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-jpa</artifactId>
                <version>${com.querydsl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-apt</artifactId>
                <version>${com.querydsl.version}</version>
            </dependency>
            <!--业务数据库版本管理工具-->
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${org.flywaydb.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql.version}</version>
            </dependency>
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>

            <!--KAFKA依赖-->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>${spring.kafka.version}</version> <!-- 请根据你的需求选择适当的版本 -->
            </dependency>
            <!--Trino依赖驱动-->
            <dependency>
                <groupId>io.trino</groupId>
                <artifactId>trino-jdbc</artifactId>
                <version>${trino.version}</version>
            </dependency>


            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.fhs-opensource</groupId> <!-- VO 数据翻译 -->
                <artifactId>easy-trans-spring-boot-starter</artifactId>
                <version>${easy.trans.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework.cloud</groupId>
                        <artifactId>spring-cloud-commons</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.fhs-opensource</groupId>
                <artifactId>easy-trans-anno</artifactId>
                <version>${easy.trans.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${org.redisson}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-actuator</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${org.springframework.version}</version>
            </dependency>

            <!--    工具类-->

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.verion}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId> <!-- 文件类型的识别 -->
                <version>${tika-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId> <!-- 解决 ThreadLocal 父子线程的传值问题 -->
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId> <!-- 解决 ftp 连接 -->
                <version>${commons-net.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId> <!-- 解决 sftp 连接 -->
                <version>${jsch.version}</version>
            </dependency>

            <dependency>
                <groupId>com.xingyuv</groupId>
                <artifactId>spring-boot-starter-captcha-plus</artifactId>
                <version>${captcha-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>

            <dependency>
                <artifactId>slf4j-log4j12</artifactId>
                <groupId>org.slf4j</groupId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>ma.glasnost.orika</groupId>
                <artifactId>orika-core</artifactId>
                <version>${orika.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${com.fasterxml.jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${com.fasterxml.jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${com.fasterxml.jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${com.squareup.okhttp}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.android</groupId>
                        <artifactId>android</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>c3p0</groupId>
                        <artifactId>c3p0</artifactId>
                    </exclusion>
                </exclusions>
                <version>${quartz.version}</version>
            </dependency>
            <!--    敏感词过滤-->
            <dependency>
                <groupId>com.netease.yidun</groupId>
                <artifactId>yidun-java-sdk</artifactId>
                <version>${yidun-java-sdk}</version>
            </dependency>

            <!-- MinIO Client Dependency -->
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp-sse</artifactId>
                <version>${okhttp-sse.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-sts</artifactId>
                <version>${com.tencentcloudapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-speech-sdk-java</artifactId>
                <version>${tencentcloud-speech-sdk-java}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
package com.center.framework.db.auditor;

import com.center.framework.common.context.LoginContextHolder;
import java.util.Optional;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;


/**
 * 通过AuditorAware<T>接口自定义获取当前登录用户ID
 * 给BaseModel中creator_id和updater_id这两个字段赋值（通过"@LastModifiedBy"和"CreatedBy"两个标签）
 */

@Component
public class SpringSecurityAuditorAware implements AuditorAware<Long> {

  @Override
  public Optional<Long> getCurrentAuditor() {
    Long userId = LoginContextHolder.getLoginUserId();
    // 如果用户ID为null（比如在异步线程中），返回空的Optional而不是抛出异常
    return userId != null ? Optional.of(userId) : Optional.empty();
  }
}

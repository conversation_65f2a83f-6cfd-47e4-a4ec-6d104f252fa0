package com.center.framework.pdf.component;

import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.transaction.Transactional;
import java.io.File;
import java.io.IOException;
import java.util.UUID;

/**
 * 支持转换为pdf的格式
 * 1、文本文档：*.txt, *.xml, *.docx, *.wps, *.html, *.odm, *.sgl, *.odt,*.ott, *.sxw, *.stw, *.fodt, *.docm, *.dotx, *.dotm, *.doc, *.dot,  *.pdb, *.hwp, *.htm, *.lwp, *.psw, *.rft, *.sdw, *.vor, *.wpd ,*.oth.
 * 2、电子表格：*.ods, *.ots, *.sxc, *.stc, *.fods, *.xml, *.xlsx, *.xlsm, *.xltm, *.xltx, *.xlsb, *.xls, *.xlc, *.xlm, *.xlw, *.xlk, *.sdc, *.vor, *.dif,*.wk1, *.wks, *.123, *.pxl, *.wb2, *.csv.
 * 3、演示文稿：*.pptx, *.odp, *.otp, *.sti, *.sxd, *.fodp, *.xml, *.pptm, *.ppsx, *.potm, *.potx, *.ppt, *.pps, *.pot, *.sdd, *.vor, *.sdp.
 * 4、绘图：*.odg, *.otg, *.sxd, *.std, *.sgv,*.sda, *.vor, *.sdd, *.cdr, *.svg, *.vsd, *.vst
 * 5、网页：*.html, *.htm, *.stw
 * 6、主控文档：*.sxg
 * 7、公式：*.odf, *.sxm, *.smf, *.mml
 * 8、数据库文档：*. odb
 */

/**
 * 需求：安装libreOffice，官网下载：https://zh-cn.libreoffice.org/download/libreoffice/
 * 当前用的版本：LibreOffice 24.8.3.2（可以从官网下载ubuntu系统下的安装包deb）
 * cd {download_url}/LibreOffice_24.8/DEBS/
 * dpkg -i *.deb
 * vi /etc/profile
 * export LibreOffice_PATH=/opt/libreoffice24.8/program
 * export PATH=$LibreOffice_PATH:$PATH
 * source /etc/profile
 */
@Slf4j
@Component
public class PDFConvert {

    @Transactional
    public String toPdf(String filePath, String targetFolder) {
        File sourceFile = new File(filePath);
        String sourceFileName = sourceFile.getName();
        int lastDotIndex = sourceFileName.lastIndexOf('.');

        if (lastDotIndex > 0) {
            sourceFileName = sourceFileName.substring(0, lastDotIndex);
        }

        String targetFileName = sourceFileName + ".pdf";
        String desPath = targetFolder + "/" + targetFileName; // 最终目标文件名

        String command = "";
        String osName = System.getProperty("os.name");

        if (osName.contains("Windows")) {
            command = "soffice --headless --convert-to pdf \"" + filePath + "\" --outdir \"" + targetFolder + "\"";
            windowExec(command);
        } else {
            command = "soffice --headless --convert-to pdf " + filePath + " --outdir " + targetFolder;
            LinuxExec(command);
        }
        return desPath;
    }

    private boolean windowExec(String command) {
        Process process;// Process可以控制该子进程的执行或获取该子进程的信息
        try {
            // exec()方法指示Java虚拟机创建一个子进程执行指定的可执行程序，并返回与该子进程对应的Process对象实例。
            process = Runtime.getRuntime().exec(command);
        } catch (IOException e) {
            log.error("异常了：" + e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,e,"文件转换成pdf出错！");
        }
        int exitStatus = 0;
        try {
            // 等待子进程完成再往下执行，返回值是子线程执行完毕的返回值,返回0表示正常结束
            exitStatus = process.waitFor();
            // 第二种接受返回值的方法
            // 接收执行完毕的返回值
            int i = process.exitValue();
            if (i != 0) {
                windowExec(command);
            }
        } catch (InterruptedException e) {
            log.error("异常了：" + e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,e,"文件转换成pdf出错！");
        }finally {
            // 确保进程资源被释放
            if (process != null) {
                process.destroy();
            }
        }
        return true;
    }

    private boolean LinuxExec(String cmd) {
        Process process=null;
        try {
            process =  Runtime.getRuntime().exec(cmd);
            int exitStatus = 0;
            try {
                exitStatus = process.waitFor();// 等待子进程完成再往下执行，返回值是子线程执行完毕的返回值,返回0表示正常结束
                // 第二种接受返回值的方法
                int i = process.exitValue(); // 接收执行完毕的返回值
                if (i != 0) {
                    LinuxExec(cmd);
                }
            } catch (InterruptedException e) {
                log.error("进程被中断", e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,e,"转换服务调用异常");
            }
        } catch (IOException e) {
            log.error("输入/输出异常", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION,e,"转换服务调用异常");
        }finally {
            // 确保进程资源被释放
            if (process != null) {
                process.destroy();
            }
        }
        return true;
    }
}

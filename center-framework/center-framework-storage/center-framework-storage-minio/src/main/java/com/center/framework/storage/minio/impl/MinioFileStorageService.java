package com.center.framework.storage.minio.impl;


import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.pdf.component.PDFConvert;
import com.center.framework.storage.interfaces.FileStorageService;
import com.center.framework.storage.interfaces.enums.FileCategoryEnum;
import com.center.framework.storage.interfaces.pojo.FileListResp;
import com.center.framework.storage.interfaces.pojo.FileMetadata;
import com.center.framework.storage.interfaces.template.FileTemplate;
import io.minio.*;
import io.minio.errors.*;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.List;


/**
 * 基于 MinIO 的文件存储服务实现。
 */
@Service
@Slf4j
@ConditionalOnProperty(name = "storage.type", havingValue = "MINIO")
public class MinioFileStorageService implements FileStorageService {
    @Autowired
    private FileTemplate fileTemplate;

    private final MinioClient minioClient;

    @Value("${minio.bucket}")
    private String bucketName;

    @Value("${minio.endpoint}")
    private String endpoint;

    @Resource
    private PDFConvert pdfConvert;

    @Override
    public void deleteDirectory(String rootPath) throws IOException, InvalidPathException {
        if (rootPath == null || rootPath.trim().isEmpty()) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.PARAM_MISMATCH, "根路径不能为空");
        }

        try {
            // 使用 normalize 方法统一路径格式为 MinIO 可识别的 object key 前缀
            String prefix = normalizeToObjectKey(rootPath);
            if (!prefix.endsWith("/")) {
                prefix += "/";
            }

            // 列出所有以 prefix 为前缀的对象
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucketName)
                            .prefix(prefix)
                            .recursive(true)
                            .build()
            );

            List<DeleteObject> objectsToDelete = new ArrayList<>();
            for (Result<Item> result : results) {
                Item item = result.get();
                objectsToDelete.add(new DeleteObject(item.objectName()));
            }

            if (objectsToDelete.isEmpty()) {
                log.warn("MinIO 中未找到路径 [{}] 下的任何文件，无需删除", rootPath);
                return;
            }

            // 批量删除对象
            Iterable<Result<DeleteError>> errors = minioClient.removeObjects(
                    RemoveObjectsArgs.builder()
                            .bucket(bucketName)
                            .objects(objectsToDelete)
                            .build()
            );

            for (Result<DeleteError> errorResult : errors) {
                DeleteError error = errorResult.get();
                log.error("删除 MinIO 对象 [{}] 失败，原因：{}", error.objectName(), error.message());
            }

            log.info("成功删除 MinIO 中路径 [{}] 下的所有文件，共 {} 个对象", rootPath, objectsToDelete.size());

        } catch (Exception e) {
            log.error("删除 MinIO 路径 [{}] 下的文件失败", rootPath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, e, "删除 MinIO 目录失败");
        }
    }


    @Override
    public void uploadFile(String sourcePath, String targetPath) {
        File file = new File(sourcePath);

        try (InputStream inputStream = new FileInputStream(file)) {
            // 获取文件的 content-type，可根据需要用更精准的方式识别
            String contentType = Files.probeContentType(file.toPath());
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            // 清洗 targetPath
            String objectKey = normalizeToObjectKey(targetPath);

            // 上传到 MinIO
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectKey)
                            .stream(inputStream, file.length(), -1)
                            .contentType(contentType)
                            .build()
            );

            log.info("文件 [{}] 成功上传到 MinIO，目标路径：{}", sourcePath, targetPath);
        } catch (Exception e) {
            log.error("上传文件到 MinIO 失败，源文件：{}，目标路径：{}", sourcePath, targetPath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, e, "上传文件到 MinIO 失败");
        }
    }

    @Override
    public void copyToLocal(String originPath, String tempFilePath) {
        try {
            // 清洗 originPath，使其匹配 MinIO 中的 key
            String objectKey = normalizeToObjectKey(originPath);

            try (InputStream inputStream = minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectKey)
                            .build())) {

                Files.copy(inputStream, Paths.get(tempFilePath), StandardCopyOption.REPLACE_EXISTING);
                log.info("成功将 MinIO 文件 [{}] 下载到本地路径 [{}]", objectKey, tempFilePath);

            }
        } catch (Exception e) {
            log.error("下载 MinIO 文件 [{}] 到本地 [{}] 时发生异常", originPath, tempFilePath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, e, "MinIO 下载文件到本地失败");
        }
    }

    public static String normalizeToObjectKey(String path) {
        if (path.startsWith("hdfs://")) {
            path = path.replaceFirst("^(hdfs://[^/]+:\\d+)", "");
        }
        return path.startsWith("/") ? path.substring(1) : path;
    }



    @Override
    public List<String> getFileList(String dirPath) {
        List<String> result = new ArrayList<>();
        try {
            // 去掉首尾 / 保证 MinIO 接口友好
            String normalizedPrefix = normalizeToObjectKey(dirPath);

            Iterable<Result<Item>> objects = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucketName)
                            .prefix(normalizedPrefix)
                            .recursive(true) // 递归列出所有文件
                            .build()
            );

            for (Result<Item> itemResult : objects) {
                Item item = itemResult.get();
                if (!item.isDir()) {
                    result.add(item.objectName());
                }
            }

            log.info("成功获取 MinIO [{}] 下文件列表，共 {} 个文件", dirPath, result.size());

        } catch (Exception e) {
            log.error("MinIO 查询文件列表 [{}] 时异常", dirPath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR, e, "MinIO 查询文件列表失败");
        }

        return result;
    }

    /**
     * 构造方法，注入 MinioClient 对象。
     *
     * @param minioClient MinioClient 对象
     */
    public MinioFileStorageService(MinioClient minioClient) {
        this.minioClient = minioClient;
    }

    @Override
    public void deleteParentDirectoryOfFilePath(List<String> filePaths) {
        for (String filePath : filePaths) {
            try {
                // 使用 normalize 方法统一路径格式为 MinIO 可识别的 object key 前缀
                String normalizedPath = normalizeToObjectKey(filePath);

                int lastSlashIndex = normalizedPath.lastIndexOf('/');
                if (lastSlashIndex <= 0) {
                    log.warn("文件路径 [{}] 没有父目录，跳过删除", filePath);
                    continue;
                }
                String parentPrefix = normalizedPath.substring(0, lastSlashIndex + 1); // 保留结尾 /

                // 列出所有以 prefix 为前缀的对象
                Iterable<Result<Item>> results = minioClient.listObjects(
                        ListObjectsArgs.builder()
                                .bucket(bucketName)
                                .prefix(parentPrefix)
                                .recursive(true)
                                .build()
                );

                List<DeleteObject> objectsToDelete = new ArrayList<>();
                for (Result<Item> result : results) {
                    Item item = result.get();
                    objectsToDelete.add(new DeleteObject(item.objectName()));
                }

                if (objectsToDelete.isEmpty()) {
                    log.warn("MinIO 中未找到路径 [{}] 下的任何文件，无需删除", parentPrefix);
                    return;
                }

                // 批量删除对象
                Iterable<Result<DeleteError>> errors = minioClient.removeObjects(
                        RemoveObjectsArgs.builder()
                                .bucket(bucketName)
                                .objects(objectsToDelete)
                                .build()
                );

                for (Result<DeleteError> errorResult : errors) {
                    DeleteError error = errorResult.get();
                    log.error("删除 MinIO 对象 [{}] 失败，原因：{}", error.objectName(), error.message());
                }

            } catch (Exception e) {
                log.error("MinIO 删除文件 [{}] 的父目录时异常", filePath, e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "MinIO 删除文件父目录失败");
            }
        }
    }

    @Override
    public void uploadFile(String targetPath, InputStream inputStream, Boolean overwrite) throws IOException {
        try {
            // 检查桶是否存在，不存在则创建
            boolean found = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!found) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                log.info("桶不存在，已创建新的桶：{}", bucketName);
            }

            // 如果文件不允许覆盖，检查文件是否已存在
            if (!overwrite && checkFileIsExist(bucketName, targetPath)) {
                // 文件已存在，抛出文件已存在的错误
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILENAME_EXIST_ERROR, targetPath);
            }

            // 上传文件
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(targetPath)  // 上传的文件路径
                            .stream(inputStream, -1, 67108864)  // 文件大小限制
                            .build()
            );
            log.info("文件上传成功：{}", targetPath);
        } catch (Exception e) {
            // 其他异常，抛出通用的 I/O 错误
            log.error("文件上传失败：{}", targetPath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件上传失败：" + e.getMessage());
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                log.warn("关闭输入流时发生异常", e);
            }
        }
    }





    @Override
    public void deleteFile(String filePath) throws IOException {
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(filePath)
                            .build()
            );
            log.info("文件删除成功：{}", filePath);
        } catch (ErrorResponseException e) {
            if ("NO_SUCH_KEY".equals(e.errorResponse().code())) {
                log.warn("文件不存在，无法删除：{}", filePath);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILE_NOT_FOUND, filePath);
            } else {
                log.error("文件删除失败：{}", filePath, e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.DELETE_OBJECT_ERROR, e, filePath);
            }
        } catch (Exception e) {
            log.error("文件删除失败：{}", filePath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件删除失败");
        }
    }

    @Override
    public InputStream downloadFile(String filePath) throws IOException {
        try {
            GetObjectArgs args = GetObjectArgs.builder()
                    .bucket(bucketName)
                    .object(filePath)
                    .build();
            InputStream stream = minioClient.getObject(args);
            log.info("文件下载成功：{}", filePath);
            return stream;
        } catch (ErrorResponseException e) {
            if ("NO_SUCH_KEY".equals(e.errorResponse().code())) {
                log.warn("文件不存在，无法下载：{}", filePath);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILE_NOT_FOUND, filePath);
            } else {
                log.error("文件下载失败：{}", filePath, e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件下载失败");
            }
        } catch (Exception e) {
            log.error("文件下载失败：{}", filePath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件下载失败");
        }
    }

    @Override
    public FileMetadata getFileMetadata(String filePath) throws IOException {
        try {
            StatObjectResponse stat = minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(filePath)
                            .build()
            );
            log.info("获取文件元数据成功：{}", filePath);
            // 将 lastModified() 转换为毫秒时间戳
            long lastModifiedMillis = stat.lastModified().toInstant().toEpochMilli();
            return new FileMetadata(
                    filePath,
                    stat.size(),
                    lastModifiedMillis,
                    filePath
            );
        } catch (ErrorResponseException e) {
            if ("NO_SUCH_KEY".equals(e.errorResponse().code())) {
                log.warn("文件不存在，无法获取元数据：{}", filePath);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILE_NOT_FOUND, filePath);
            } else {
                log.error("获取文件元数据失败：{}", filePath, e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "获取文件元数据失败");
            }
        } catch (Exception e) {
            log.error("获取文件元数据失败：{}", filePath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "获取文件元数据失败");
        }
    }

    @Override
    public ResponseEntity<ByteArrayResource> previewFile(String filePath) throws IOException {
        byte[] content;

        try (InputStream inputStream = minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(bucketName) // 指定存储桶名称
                        .object(filePath)   // 指定文件路径
                        .build())) {

            // 将文件内容读取到字节数组
            content = readAllBytes(inputStream);

            // 记录日志
            log.info("文件预览成功：{}", filePath);
        } catch (ErrorResponseException e) {
            // 处理文件不存在的情况
            if ("NO_SUCH_KEY".equals(e.errorResponse().code())) {
                log.warn("文件不存在，无法预览：{}", filePath);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FILE_NOT_FOUND, filePath);
            } else {
                log.error("文件预览失败：{}", filePath, e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件预览失败");
            }
        } catch (Exception e) {
            log.error("文件预览失败：{}", filePath, e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.IO_ERROR, e, "文件预览失败");
        }

        //获取相应头
        HttpHeaders headers = fileTemplate.getHeaders(filePath);
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(content.length)
                .body(new ByteArrayResource(content));
    }

    @Override
    public String convertPdf(String originPath) {
        String pdfName = originPath.substring(originPath.lastIndexOf("/") + 1);
        File pdfFile = null;
        File crcfile = null;
        InputStream inputStream= null;
        InputStream pdfInputStream= null;
        FileOutputStream outputStream = null;
        if (!pdfName.endsWith(".pdf")) {
            int lastDotIndex = pdfName.lastIndexOf('.');
            if (lastDotIndex > 0) {
                pdfName = pdfName.substring(0, lastDotIndex);
            }
            pdfName = pdfName + ".pdf";
            File localTempFile = null;
            try {
                localTempFile = File.createTempFile("temp", ".odt");
                String tempFilePath = localTempFile.getAbsolutePath();
                // 读取文件并写入到本地临时文件
                inputStream = downloadFile(originPath);
                outputStream = new FileOutputStream(tempFilePath);
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                // 转换为 PDF
                String pdfPath = pdfConvert.toPdf(tempFilePath,tempFilePath.substring(0, tempFilePath.lastIndexOf(File.separator)));
                // 上传文件
                pdfInputStream = new FileInputStream(pdfPath);
                String targetPath = originPath.substring(0, originPath.lastIndexOf("/")) + "/" + pdfName;
                uploadFile(targetPath, pdfInputStream,true);
                pdfFile = new File(pdfPath);
                String crcPath = localTempFile.getParent() + "\\\\." + localTempFile.getName() + ".crc";
                crcfile = new File(crcPath);
                return targetPath;
            } catch (IOException e) {
                log.error("文件上传异常", e);
                throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e);
            } finally {
                try {
                    if (inputStream != null) {
                        inputStream.close();
                    }
                    if (pdfInputStream != null) {
                        pdfInputStream.close();
                    }
                    if(outputStream != null){
                        outputStream.close();
                    }
                } catch (IOException e) {
                    log.warn("关闭输入流时发生异常", e);
                }
                // 删除临时文件
                if (localTempFile != null && localTempFile.exists()) {
                    localTempFile.delete();
                }
                if (pdfFile != null && pdfFile.exists()) {
                    pdfFile.delete();
                }
                if (crcfile != null && crcfile.exists()) {
                    crcfile.delete();
                }
            }
        }
        return originPath;
    }

    public byte[] readAllBytes(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, bytesRead);
        }
        return buffer.toByteArray();
    }
    @Override
    public List<FileListResp> getAllFiles(String path) {
        List<FileListResp> fileListResps = new ArrayList<>();
        try {
            // MinIO 获取文件列表
            // 规范路径（去掉 hdfs:// 或前缀 /）
            String prefix = normalizeToObjectKey(path);
            if (!prefix.endsWith("/")) {
                prefix += "/";
            }

            Iterable<Result<Item>> objects = minioClient.listObjects(ListObjectsArgs.builder().bucket(bucketName).prefix(prefix).recursive(false).build());

            for (Result<Item> result : objects) {
                Item item = result.get();
                String objectName = item.objectName();
                String fileName = objectName.substring(prefix.length()); // 去掉前缀

                FileListResp fileListResp = new FileListResp();
                fileListResp.setFileName(fileName);
                fileListResp.setFilePath(endpoint + "/"+ bucketName+ "/" + item.objectName());
                fileListResp.setFileCategory(item.isDir() ? FileCategoryEnum.DIR : FileCategoryEnum.FILE);
                // 这里设置额外的信息
                // 设置最后修改时间：将 Instant 转换为 Date
                fileListResp.setFileSize(item.size());  // MinIO 返回文件大小
                fileListResps.add(fileListResp);
            }
        } catch (Exception e) {
            log.error("Error fetching files from MinIO", e);
        }
        return fileListResps;
    }

    @Override
    public void copyFromCloudToCloud(String sourceFilePath, String targetFilePath) {
        try {
            String sourceObject = sourceFilePath.startsWith("/") ? sourceFilePath.substring(1) : sourceFilePath;
            String targetObject = targetFilePath.startsWith("/") ? targetFilePath.substring(1) : targetFilePath;

            CopySource source = CopySource.builder()
                    .bucket(bucketName)
                    .object(sourceObject)
                    .build();

            minioClient.copyObject(
                    CopyObjectArgs.builder()
                            .bucket(bucketName)
                            .object(targetObject)
                            .source(source)
                            .build()
            );

        } catch (Exception e) {
            log.error("MinIO copy object failed", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.UPLOAD_FILE_ERROR, e, "MinIO 云端文件复制失败");
        }
    }



    /**
     * 判断文件是否存在
     *
     * @param bucketName 桶名称
     * @param objectName 文件路径
     * @return true 存在, false 不存在
     */
    public Boolean checkFileIsExist(String bucketName, String objectName) {
        try {
            minioClient.statObject(
                    StatObjectArgs.builder().bucket(bucketName).object(objectName).build()
            );
        } catch (Exception e) {
            return false;
        }
        return true;
    }

}

package com.center.framework.web.annotation.enumconvert;

import com.center.framework.common.enumerate.IEnumerate;
import com.center.framework.common.exception.ServiceException;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;


/**
 * 枚举类转换切面
 * 根据定义的枚举类型将code转换为name。
 * 枚举类必须是继承IEnumerate，正面的方法会自动将value翻译为description
 */
@Component
@Aspect
@Slf4j
public class EnumConvertAspect {

    @AfterReturning(value = "@annotation(enumConvertPoint)",returning = "result")
    public Object afterReturning(JoinPoint joinPoint, EnumConvertPoint enumConvertPoint, Object result) throws IllegalAccessException {
        if (result == null){
            return null;
        }
        if (result instanceof CommonResult){
            Object body = ((CommonResult<?>) result).getData();
            if (body == null){
                return null;
            }
            else if (body instanceof PageResult){
                for(Object obj:((PageResult<?>)body).getList()){
                    processObj(obj);
                }
            }else if (body instanceof Collection ){
                for (Object obj : (Collection<?>) body){
                    //处理
                    processObj(obj);
                }
            }else if (body instanceof Map){
                // 处理 Map 类型，遍历 Map 的所有值
                Map<?, ?> map = (Map<?, ?>) body;
                for (Object value : map.values()) {
                    if (value instanceof Collection) {
                        // 如果值是集合，遍历集合中的每个元素
                        for (Object obj : (Collection<?>) value) {
                            processObj(obj);
                        }
                    } else {
                        // 如果值是单个对象，直接处理
                        processObj(value);
                    }
                }
            }else {
                //处理
                processObj(body);
            }
        }else if (result instanceof Collection){
            //处理
            for (Object obj : (Collection<?>) result){
                //处理
                processObj(obj);
            }
        }else {
            processObj(result);

        }
        return result;
    }

    private void processObj(Object obj) throws IllegalAccessException {
        //取出Obj所有 Field，以及父类 Field
        Class<?> tempClass = obj.getClass();
        while (tempClass != null) {
            for (Field field : tempClass.getDeclaredFields()) {
                //遍历Field,查找添加 @EnumConvert 注解的字段并做翻译
                if (field.isAnnotationPresent(EnumConvert.class)) {
                    try {
                        boolean fieldAccess = field.isAccessible();
                        field.setAccessible(true);
                        EnumConvert enumConvert = field.getAnnotation(EnumConvert.class);
                        Field srcField = tempClass.getDeclaredField(enumConvert.srcFieldName());
                        boolean srcFieldAccess = srcField.isAccessible();
                        srcField.setAccessible(true);
                        if(srcField.get(obj) == null){
                            field.setAccessible(fieldAccess);
                            continue;
                        }
                        String code = srcField.get(obj).toString();
                        srcField.setAccessible(srcFieldAccess);
                        for (IEnumerate<?> IEnumerate : enumConvert.value().getEnumConstants()) {
                            if (code.equals(IEnumerate.getValue())) {
                                field.set(obj, IEnumerate.getDescription());
                                break;
                            }
                        }
                        field.setAccessible(fieldAccess);
                    } catch (NoSuchFieldException e) {
                        log.error("枚举类型value转description出错,指定的原字段不正确", e);
                        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "枚举类型value转description出错,指定的原字段不正确！");
                    }
                }
            }
            tempClass = tempClass.getSuperclass();
        }
    }

}

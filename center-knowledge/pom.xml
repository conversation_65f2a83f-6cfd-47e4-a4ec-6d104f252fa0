<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>knowledge</artifactId>
        <groupId>com.center</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>center-knowledge</artifactId>
    <packaging>jar</packaging>

    <dependencies>
       <!-- <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-storage-factory</artifactId>
            <version>${revision}</version>
        </dependency>-->
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-storage-factory</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-cache-factory</artifactId>
            <version>${revision}</version>
        </dependency>
        <!--hadoop-->
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-hdfs</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-common</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-reload4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.hadoop</groupId>
            <artifactId>hadoop-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-db</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-web</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-infrastructure-system</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.center</groupId>
            <artifactId>center-framework-pdf</artifactId>
            <version>${revision}</version>
        </dependency>

        <!--    敏感词过滤-->
        <dependency>
            <groupId>com.netease.yidun</groupId>
            <artifactId>yidun-java-sdk</artifactId>
        </dependency>
        <!--test-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <!--解决JDK 8的时间类型默认不支持JSON序列化/反序列化-->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.13.5</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp-sse</artifactId>
        </dependency>

        <!-- EasyExcel for Excel file processing -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <!---Tencent语音服务SDK-->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-sts</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-speech-sdk-java</artifactId>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>com.mysema.maven</groupId>
                <artifactId>apt-maven-plugin</artifactId>
                <version>1.1.3</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>process</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>target/generated-sources/java</outputDirectory>
                            <processor>com.querydsl.apt.jpa.JPAAnnotationProcessor</processor>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
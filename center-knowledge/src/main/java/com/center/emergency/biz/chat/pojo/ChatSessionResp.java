package com.center.emergency.biz.chat.pojo;

import com.center.emergency.common.enumeration.ChatTypeEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ChatSessionResp {

    @Schema(description = "SessionID")
    private Long id;

    @Schema(description = "历史对话标题")
    private String title;

    @Schema(description = "对话类型", example = "NORMAL")
    private ChatTypeEnum chatType;

    @EnumConvert(value = ChatTypeEnum.class, srcFieldName = "chatType")
    @Schema(description = "对话类型名称", example = "普通对话")
    private String chatTypeName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "会话创建时间")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "最新活动时间（最新回答时间或会话创建时间）")
    private LocalDateTime lastActivityTime;
}

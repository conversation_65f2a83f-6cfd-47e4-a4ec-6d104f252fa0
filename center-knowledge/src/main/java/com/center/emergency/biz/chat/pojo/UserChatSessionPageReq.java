package com.center.emergency.biz.chat.pojo;

import com.center.framework.web.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 用户历史会话分页查询请求
 */
@Data
@Schema(description = "用户历史会话分页查询请求")
public class UserChatSessionPageReq extends PageParam {
    
    @Schema(description = "会话标题关键词（模糊搜索）")
    @Length(max = 100, message = "搜索关键词长度不能超过100个字符")
    private String keyword;
    
    @Schema(description = "机器人名称关键词（模糊搜索）")
    @Length(max = 50, message = "机器人名称关键词长度不能超过50个字符")
    private String robotName;
}

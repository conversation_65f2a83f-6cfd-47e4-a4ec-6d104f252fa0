package com.center.emergency.biz.chat.pojo;

import com.center.emergency.common.enumeration.ChatTypeEnum;
import com.center.framework.web.annotation.enumconvert.EnumConvert;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户历史会话分页响应对象
 */
@Data
@Schema(description = "用户历史会话分页响应对象")
public class UserChatSessionPageResp {

    @Schema(description = "会话ID")
    private Long id;

    @Schema(description = "会话标题")
    private String title;

    @Schema(description = "机器人ID")
    private Long robotId;

    @Schema(description = "机器人名称")
    private String robotName;

    @Schema(description = "对话类型", example = "NORMAL")
    private ChatTypeEnum chatType;

    @EnumConvert(value = ChatTypeEnum.class, srcFieldName = "chatType")
    @Schema(description = "对话类型名称", example = "普通对话")
    private String chatTypeName;

    @Schema(description = "会话创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "最新活动时间（最新回答时间或会话创建时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastActivityTime;

    @Schema(description = "对话轮次数量")
    private Long conversationCount;
}

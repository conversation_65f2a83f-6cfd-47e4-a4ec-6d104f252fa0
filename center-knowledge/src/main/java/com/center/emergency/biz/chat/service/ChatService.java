package com.center.emergency.biz.chat.service;

import com.center.emergency.biz.chat.common.persitence.ParsedEvent;
import com.center.emergency.biz.chat.pojo.*;
import com.center.emergency.biz.robot.pojo.RobotPreviewResp;
import com.center.framework.web.pojo.PageResult;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;

public interface ChatService {

    List<RobotPreviewResp> searchAllRobot();

    Map<String, List<ChatSessionResp>> searchAllSession(ChatSessionSearchReq chatSessionSearchReq);

    void thumbsUp(Long id);

    void thumbsDown(Long id);

    List<ChatHistoryResp> searchSessionHistory(Long sessionId);


    /**
     * 根据提供的机器人名称与描述，调用算法服务接口，自动生成对话示例问题列表。
     *
     * @param quickQuestionsReq 包含机器人名称和描述的请求参数对象
     * @return 生成的对话示例问题列表
     */
    List<String> autoGenerateQuickQuestions(QuickQuestionsReq quickQuestionsReq);

    SseEmitter chatWithKnowledge(ChatWithKnowledgeReq chatWithRobotReq);

    SseEmitter reAnswer(Long answerId);

    SseEmitter chatWithRobot(ChatWithRobotReq chatWithRobotReq, Boolean isSimulate);

    /**
     * 与Agent进行模拟对话。不需要保存对话历史
     * @param simulateChatRobotReq
     * @return
     */
    SseEmitter simulatedChatWithRobot(SimulateChatRobotReq simulateChatRobotReq);

    /**
     * 根据提供的机器人名称、描述和任务类型，调用算法服务接口，智能生成对应的内容（如开场白或系统提示词）。
     *
     * @param autoGenerationReq 包含机器人名称、描述和任务类型的请求参数对象
     * @return 生成的对应内容字符串，任务类型为 {@code OPENING_REMARK} 时返回开场白，
     *         为 {@code SYSTEM_PROMPT} 时返回系统提示词
     */
    String autoGeneration(AutoGenerationReq autoGenerationReq);

    /**
     * 保存问题、回答和所有事件
     *
     * @param chatVO 对话上下文
     * @param events 所有事件列表
     * @param isSimulate 是否模拟模式
     */
    void saveQuestionAndAnswerWithEvents(ChatVO chatVO, List<ParsedEvent> events, Boolean isSimulate);

    void updateTitle(UpdateTitleReq updateTitleReq);

    /**
     * 根据SessionID删除会话
     * @param sessionId
     */
    void deleteSession(Long sessionId);
}

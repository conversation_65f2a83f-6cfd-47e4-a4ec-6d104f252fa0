package com.center.emergency.biz.chat.service;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.center.emergency.biz.chat.common.enumeration.EventStatusEnum;
import com.center.emergency.biz.chat.common.enumeration.ParsedEventType;
import com.center.emergency.biz.chat.common.listener.ChatSSEListener;
import com.center.emergency.biz.chat.common.parser.ChatSseParser;
import com.center.emergency.biz.chat.common.parser.StreamParser;
import com.center.emergency.biz.chat.common.persitence.ParsedEvent;
import com.center.emergency.biz.chat.persistence.*;
import com.center.emergency.biz.chat.pojo.*;
import com.center.emergency.biz.datasource.persistence.QDataSourceExtensionModel;
import com.center.emergency.biz.datasource.persistence.QDataSourceModel;
import com.center.emergency.biz.datasource.pojo.DataBaseTableInfo;
import com.center.emergency.biz.files.persistence.FileModel;
import com.center.emergency.biz.files.persistence.FileRepository;
import com.center.emergency.biz.files.pojo.FileInfo;
import com.center.emergency.biz.files.service.FileComponent;
import com.center.emergency.biz.files.service.FileService;
import com.center.emergency.biz.knowledge.persistence.QKnowledgeModel;
import com.center.emergency.biz.knowledgebase.persistence.QKnowledgeBaseModel;
import com.center.emergency.biz.mcp.persistence.QMcpModel;
import com.center.emergency.biz.robot.persitence.QRobotModel;
import com.center.emergency.biz.robot.factory.AnswerStrategyFactory;
import com.center.emergency.biz.robot.persitence.QRobotKnowledgeModel;
import com.center.emergency.biz.robot.persitence.RobotKnowledgeModel;
import com.center.emergency.biz.robot.persitence.RobotModel;
import com.center.emergency.biz.robot.persitence.RobotRepository;
import com.center.emergency.biz.robot.service.AnswerStrategyService;
import com.center.emergency.biz.robot.service.DataBaseServiceForAnswerStrategy;
import com.center.emergency.common.enumeration.*;
import com.center.emergency.biz.robot.pojo.RobotPreviewResp;
import com.center.emergency.common.utils.AntispamUtil;
import com.center.emergency.common.utils.OkHttpUtils;
import com.center.framework.common.context.LoginContextHolder;

import com.center.framework.common.enumerate.CommonStatusEnum;
import com.center.framework.common.enumerate.EventTypeEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.CommonResult;
import com.center.framework.web.pojo.PageResult;
import com.center.infrastructure.system.biz.depart.persistence.DepartRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.OBJECT_NOT_EXISTED;
import static com.center.framework.common.exception.constant.GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION;
import com.center.emergency.biz.chat.common.agentchat.AgentChatUtils;

@Service
@Slf4j
public class ChatServiceImpl implements ChatService {

    @Resource
    AntispamUtil antispamUtil;

    @Resource
    Snowflake snowflake;

    @Resource
    private Executor seeExecutor;

    @Autowired
    private JPAQueryFactory jpaQueryFactory;


    @Value("${python.api-base-url}")
    private String modelUrl;

    @Value("${python.chat-api-base-url}")
    private String chatBaseUrl;


    @Resource
    private JPAQueryFactory queryFactory;

    @Resource
    private RobotRepository robotRepository;

    @Resource
    private ChatQuestionRepository chatQuestionRepository;

    @Resource
    private ChatAnswerRepository chatAnswerRepository;

    @Resource
    private ChatAnswerEventRepository chatAnswerEventRepository;

    @Resource
    private ChatSessionRepository chatSessionRepository;

    @Resource
    private FileRepository fileRepository;

    @Resource
    private ChatParameterService chatParameterService;

    @Resource
    private AnswerStrategyFactory answerStrategyFactory;

    @Resource
    private PlatformTransactionManager transactionManager;

    @Resource
    private ChatResourceService chatResourceService;

    @Resource
    private ChatTagService chatTagService;

    @Resource
    private ChatHistoryService chatHistoryService;

    @Resource
    private ChatApiUrlService chatApiUrlService;

    @Resource
    private FileService fileService;

    private static String CHAT_DEFAULT_ERROR_MESSAGE = "系统正忙，请稍后再试。";

    @Resource
    private FileComponent fileComponent;

    @Resource
    private DataBaseServiceForAnswerStrategy dataBaseServiceForAnswerStrategy;

    @Resource
    private DepartRepository departRepository;

    @Override
    public SseEmitter chatWithRobot(ChatWithRobotReq chatWithRobotReq, Boolean isSimulate) {
        // ========== 机器人对话场景 ==========
        // 特点：使用 newBuildRequestParam，包含完整的模型配置参数
        // 参数：isSimulate 可变，modelId 从机器人配置获取
        
        Long modelGroupId = checkRobot(chatWithRobotReq.getRobotId());

        ChatVO chatVO = OrikaUtils.convert(chatWithRobotReq, ChatVO.class);
        if (!isSimulate) {
            chatVO.setQuestionId(snowflake.nextId());
            chatVO.setAnswerId(snowflake.nextId());
            if (chatWithRobotReq.getSessionId() == null) {
                chatVO.setSessionId(snowflake.nextId());
            }
        }

        // 设置对话类型，如果请求中没有指定则根据 isSimulate 参数设置
        if (chatVO.getChatType() == null) {
            chatVO.setChatType(isSimulate ? ChatTypeEnum.SIMULATION : ChatTypeEnum.NORMAL);
        }

        SseEmitter emitter = new SseEmitter(10 * 60 * 1000L);
        if (checkQuestionSpam(chatVO.getQuestion(), chatVO, isSimulate, emitter)) {
            return emitter;
        }

        try {
            emitter.send(SseEmitter.event().name("header").data(CommonResult.success(chatVO)));
        } catch (IOException e) {
            log.error("流式对话出错", e);
            handleException(emitter, ServiceExceptionUtil.getStackTrace(e));
            return emitter;
        }

        // ===== 使用答案策略自主收集资源并构建参数 =====
        // 获取机器人的答案策略
        AnswerStrategyEnum answerStrategy = getRobotAnswerStrategy(chatWithRobotReq.getRobotId());

        // 如果是文件策略且提供了文件ID，先将文件ID插入question表
        if (answerStrategy == AnswerStrategyEnum.FILE && chatWithRobotReq.getFileIds() != null && !chatWithRobotReq.getFileIds().isEmpty()) {
            insertFileIdsToQuestionTable(chatVO.getSessionId(), chatWithRobotReq.getFileIds(), isSimulate);
        }

        // 使用对应策略自主收集资源并构建参数
        HashMap<String, Object> param = answerStrategyFactory.getAnswerStrategyService(answerStrategy)
                .buildChatParamsWithResourceCollection(chatVO, isSimulate, modelGroupId, queryFactory);
        // 补充历史记录参数，isSimulate会影响历史记录的获取逻辑
        param.put("history", chatHistoryService.getHistory(chatVO.getSessionId(), chatVO.getQuestionId(), isSimulate, queryFactory));

        // ===== 添加可选参数 =====
        addOptionalParams(param, chatWithRobotReq.getEnableModelRouter(), chatWithRobotReq.getEnableReasoning());
        
        // 执行异步SSE请求 - 使用策略模式调用不同接口
        String apiUrl = chatApiUrlService.getApiUrl(answerStrategy);
        AgentChatUtils.executeAsyncSseChat(
            emitter,
            chatVO,
            apiUrl,
            param,
            isSimulate, // 传递实际的isSimulate参数
            seeExecutor,
            new ChatSseParser(emitter, this, false, chatVO, new StringBuilder()),
            (AgentChatUtils.ListenerFactory) (parser, em, vo, simulate) -> new ChatSSEListener((StreamParser) parser, new CountDownLatch(1), em, this, transactionManager, vo, simulate, new StringBuilder())
        );

        return emitter;
    }

    @Override
    public SseEmitter simulatedChatWithRobot(SimulateChatRobotReq simulateChatRobotReq) {
        SseEmitter emitter = new SseEmitter(10 * 60 * 1000L);

        if (checkQuestionSpam(simulateChatRobotReq.getQuestion(), null, Boolean.TRUE, emitter)) {
            return emitter;
        }
        try {
            emitter.send(SseEmitter.event().name("header").data(CommonResult.success(simulateChatRobotReq)));
        } catch (IOException e) {
            log.error("流式对话出错", e);
            handleException(emitter, ServiceExceptionUtil.getStackTrace(e));
            return emitter;
        }

        HashMap<String, Object> param = new HashMap<>();
        if(AnswerStrategyEnum.FILE.equals(simulateChatRobotReq.getAnswerStrategy())) {
            param = buildChatParamsForFile(simulateChatRobotReq.getModelGroupId(),simulateChatRobotReq.getAnswerStrategyIds(),simulateChatRobotReq.getQuestion()
            ,simulateChatRobotReq.getAnswerStrategy(),simulateChatRobotReq.getUserCustomPrompt());
        }else if(AnswerStrategyEnum.KNOWLEDGE_BASE.equals(simulateChatRobotReq.getAnswerStrategy())){
            param = buildChatParamsForKB(simulateChatRobotReq.getModelGroupId(),simulateChatRobotReq.getAnswerStrategyIds(),simulateChatRobotReq.getQuestion()
            ,simulateChatRobotReq.getAnswerStrategy(),simulateChatRobotReq.getId(),simulateChatRobotReq.getSearchMode(),simulateChatRobotReq.getAnswerMode()
            ,simulateChatRobotReq.getMaxHits(),simulateChatRobotReq.getSimilarityThreshold());
        }else if(AnswerStrategyEnum.DATABASE.equals(simulateChatRobotReq.getAnswerStrategy())){
            param = buildChatParamsForDB(simulateChatRobotReq.getModelGroupId(),simulateChatRobotReq.getAnswerStrategyIds(),simulateChatRobotReq.getQuestion()
            ,simulateChatRobotReq.getAnswerStrategy());
        }else if(AnswerStrategyEnum.MCP.equals(simulateChatRobotReq.getAnswerStrategy())){
            param = buildChatParamsForMCP(simulateChatRobotReq.getModelGroupId(),simulateChatRobotReq.getAnswerStrategyIds(),simulateChatRobotReq.getQuestion()
            ,simulateChatRobotReq.getAnswerStrategy());
        }

        param.put("history",Arrays.asList());

        String apiUrl = chatApiUrlService.getApiUrl(simulateChatRobotReq.getAnswerStrategy());
        AgentChatUtils.executeAsyncSseChat(
                emitter,
                new ChatVO(),
                apiUrl,
                param,
                Boolean.TRUE, // 传递实际的isSimulate参数
                seeExecutor,
                new ChatSseParser(emitter, this, false, new ChatVO(), new StringBuilder()),
                (AgentChatUtils.ListenerFactory) (parser, em, vo, simulate) -> new ChatSSEListener((StreamParser) parser, new CountDownLatch(1), em, this, transactionManager, vo, simulate, new StringBuilder())
        );

        return emitter;    }

    @Override
    public String autoGeneration(AutoGenerationReq autoGenerationReq) {
        try {
            // 构建请求参数
            HashMap<String, Object> param = new HashMap<>();
            param.put("name", autoGenerationReq.getRobotName());
            param.put("description", autoGenerationReq.getDescription());
            param.put("task_type", autoGenerationReq.getTaskType().getDescription());

            JsonNode rootNode = callPromptApi(param);

            String result = null;
            if (autoGenerationReq.getTaskType().equals(AutoGenerationTaskTypeEnum.OPENING_REMARK)) {
                result = rootNode.path("welcome_message").asText();
            } else if (autoGenerationReq.getTaskType().equals(AutoGenerationTaskTypeEnum.SYSTEM_PROMPT)) {
                result = rootNode.path("system_prompt").asText();
            }

            return result;
        } catch (Exception e) {
            log.error("智能生成失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "智能生成失败，请稍后重试。");
        }
    }

    @Override
    public List<String> autoGenerateQuickQuestions(QuickQuestionsReq quickQuestionsReq) {
        try {
            // 构建请求参数
            HashMap<String, Object> param = new HashMap<>();
            param.put("name", quickQuestionsReq.getRobotName());
            param.put("description", quickQuestionsReq.getDescription());
            param.put("task_type", "对话示例");

            JsonNode rootNode = callPromptApi(param);

            // 取出 quick_questions 数组
            JsonNode quickQuestionsNode = rootNode.path("quick_questions");

            List<String> resultList = new ArrayList<>();
            quickQuestionsNode.forEach(item -> resultList.add(item.asText()));

            return resultList;
        } catch (Exception e) {
            log.error("智能生成失败", e);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "智能生成失败，请稍后重试。");
        }
    }

    private JsonNode callPromptApi(Map<String, Object> param) throws IOException {
        OkHttpClient client = OkHttpUtils.getOkHttpClient();
        Request request = OkHttpUtils.getJsonPostRequest(modelUrl + "/api/local_doc_qa/generate_custom_prompt", param);

        Response response = client.newCall(request).execute();
        String responseBody = response.body().string();
        log.info("算法返回结果：{}", responseBody);

        ObjectMapper mapper = new ObjectMapper();
        JsonNode rootNode = mapper.readTree(responseBody);

        int code = rootNode.path("code").asInt();
        if (code != 200) {
            String msg = rootNode.path("msg").asText("生成失败，请稍后重试。");
            log.warn("算法接口返回非200，msg: {}", msg);
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OUTER_SERVER_EXCEPTION, "算法调用失败");
        }

        return rootNode;
    }


    @Override
    @Transactional
    public SseEmitter chatWithKnowledge(ChatWithKnowledgeReq chatWithKnowledgeReq) {
        // ========== 知识库对话场景 ==========
        // 特点：使用 buildRequestParam，固定的参数配置，不包含个性化模型配置
        // 参数：isSimulate 固定为 TRUE，无 modelId
        
        List<Map<String, String>> tags = chatTagService.getTagsFromKnowledgeBase(chatWithKnowledgeReq.getKnowledgeBaseId(), queryFactory);
        Set<String> kbIds = new HashSet<>();
        List<String> fileIds = new ArrayList<>();
        chatResourceService.buildFromKnowledgeBase(chatWithKnowledgeReq.getKnowledgeBaseId(), kbIds, fileIds, queryFactory);
        ChatVO chatVO = OrikaUtils.convert(chatWithKnowledgeReq, ChatVO.class);
        // 知识库对话固定为模拟对话类型
        chatVO.setChatType(ChatTypeEnum.SIMULATION);

        SseEmitter emitter = new SseEmitter(10 * 60 * 1000L);
        if (checkQuestionSpam(chatVO.getQuestion(), chatVO, Boolean.TRUE, emitter)) {
            return emitter;
        }

        try {
            emitter.send(SseEmitter.event().name("header").data(CommonResult.success(chatVO)));
        } catch (IOException e) {
            log.error("流式对话出错", e);
            handleException(emitter, ServiceExceptionUtil.getStackTrace(e));
            return emitter;
        }

        // ===== 关键差异：使用 buildRequestParam 构建基础参数，无个性化模型配置 =====
        HashMap<String, Object> param = chatParameterService.buildKnowledgeBaseChatParams(chatVO, kbIds, fileIds, tags, Boolean.TRUE);
        // 补充历史记录参数，固定为TRUE（模拟模式）
        param.put("history", chatHistoryService.getHistory(chatVO.getSessionId(), chatVO.getQuestionId(), Boolean.TRUE, queryFactory));
        
        // 执行异步SSE请求 - 知识库对话固定使用知识库策略接口
        log.info("知识库对话-传给模型调用参数：{}", param);
        String apiUrl = chatApiUrlService.getApiUrl(AnswerStrategyEnum.KNOWLEDGE_BASE);
        AgentChatUtils.executeAsyncSseChat(
            emitter,
            chatVO,
            apiUrl,
            param,
            Boolean.TRUE, // 固定为模拟模式
            seeExecutor,
            new ChatSseParser(emitter, this, false, chatVO, new StringBuilder()),
            (AgentChatUtils.ListenerFactory) (parser, em, vo, simulate) -> new ChatSSEListener((StreamParser) parser, new CountDownLatch(1), em, this, transactionManager, vo, simulate, new StringBuilder())
        );

        return emitter;
    }

    @Override
    @Transactional
    public SseEmitter reAnswer(Long answerId) {
        // ========== 重新回答场景 ==========
        // 特点：使用 newBuildRequestParam，包含完整的模型配置参数
        // 参数：isSimulate 固定为 FALSE，modelId 从机器人配置获取
        
        QChatAnswerModel qChatAnswerModel = QChatAnswerModel.chatAnswerModel;
        QChatSessionModel qChatSessionModel = QChatSessionModel.chatSessionModel;
        QChatQuestionModel qChatQuestionModel = QChatQuestionModel.chatQuestionModel;
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qChatAnswerModel.id.eq(answerId));
        builder.and(qChatAnswerModel.sessionId.eq(qChatSessionModel.id));
        builder.and(qChatAnswerModel.questionId.eq(qChatQuestionModel.id));
        Tuple tuple =
                queryFactory
                        .select(
                                qChatAnswerModel.id,
                                qChatQuestionModel.id,
                                qChatQuestionModel.content,
                                qChatSessionModel.robotId,
                                qChatSessionModel.id)
                        .from(qChatAnswerModel, qChatSessionModel, qChatQuestionModel)
                        .where(builder)
                        .fetchFirst();

        if (null != tuple) {
            ChatVO chatVO = new ChatVO();
            chatVO.setAnswerId(tuple.get(qChatAnswerModel.id));
            chatVO.setQuestionId(tuple.get(qChatQuestionModel.id));
            chatVO.setQuestion(tuple.get(qChatQuestionModel.content));
            chatVO.setRobotId(tuple.get(qChatSessionModel.robotId));
            chatVO.setSessionId(tuple.get(qChatSessionModel.id));
            // 重新回答固定为普通对话类型
            chatVO.setChatType(ChatTypeEnum.NORMAL);

            SseEmitter emitter = new SseEmitter(10 * 60 * 1000L);
            if (checkQuestionSpam(chatVO.getQuestion(), chatVO, Boolean.TRUE, emitter)) {
                return emitter;
            }

            try {
                emitter.send(SseEmitter.event().name("header").data(CommonResult.success(chatVO)));
            } catch (IOException e) {
                log.error("流式对话出错", e);
                handleException(emitter, ServiceExceptionUtil.getStackTrace(e));
                return emitter;
            }

            // ===== 使用答案策略自主收集资源并构建参数，isSimulate固定为FALSE =====
            Long modelGroupId = checkRobot(chatVO.getRobotId());
            // 获取机器人的答案策略
            AnswerStrategyEnum answerStrategy = getRobotAnswerStrategy(chatVO.getRobotId());
            // 使用对应策略自主收集资源并构建参数
            HashMap<String, Object> param = answerStrategyFactory.getAnswerStrategyService(answerStrategy)
                    .buildChatParamsWithResourceCollection(chatVO, Boolean.FALSE, modelGroupId, queryFactory);
            // 补充历史记录参数，固定为FALSE（非模拟模式）
            param.put("history", chatHistoryService.getHistory(chatVO.getSessionId(), chatVO.getQuestionId(), Boolean.FALSE, queryFactory));
            
            // 执行异步SSE请求 - 重新回答使用原机器人的答案策略接口
            log.info("重新回答-传给模型调用参数：{}", param);
            String apiUrl = chatApiUrlService.getApiUrl(answerStrategy);
            AgentChatUtils.executeAsyncSseChat(
                emitter,
                chatVO,
                apiUrl,
                param,
                Boolean.FALSE, // 固定为非模拟模式
                seeExecutor,
                new ChatSseParser(emitter, this, false, chatVO, new StringBuilder()),
                (AgentChatUtils.ListenerFactory) (parser, em, vo, simulate) -> new ChatSSEListener((StreamParser) parser, new CountDownLatch(1), em, this, transactionManager, vo, simulate, new StringBuilder())
            );
            return emitter;
        } else {
            throw ServiceExceptionUtil.exception(
                    GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "回答不存在！", answerId);
        }
    }


    @Override
    public List<RobotPreviewResp> searchAllRobot() {
        QRobotModel qRobotModel = QRobotModel.robotModel;
        QChatSessionModel qChatSessionModel = QChatSessionModel.chatSessionModel;
        QChatAnswerModel qChatAnswerModel = QChatAnswerModel.chatAnswerModel;
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;

        Long tenantId = LoginContextHolder.getLoginUserTenantId();
        Long userId = LoginContextHolder.getLoginUserId();

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qRobotModel.tenantId.eq(tenantId));
        // 移除状态过滤，显示所有机器人（无论激活还是未激活）

        // 使用左连接获取机器人和用户的最后使用时间（最后回答时间），按最后使用时间排序
        List<RobotPreviewResp> result = queryFactory.select(Projections.bean(RobotPreviewResp.class
                        , qRobotModel.id
                        , qRobotModel.robotName
                        , qRobotModel.welcomeMessage
                        , qRobotModel.remark
                        , qRobotModel.systemPrompt
                        , qRobotModel.dialogueExamples
                        , qRobotModel.searchMode
                        , qRobotModel.answerMode
                        , qRobotModel.similarityThreshold
                        , qRobotModel.maxHits,
                        qRobotKnowledgeModel.answerStrategy))
                .from(qRobotModel)
                .leftJoin(qChatSessionModel).on(
                    qChatSessionModel.robotId.eq(qRobotModel.id)
                    .and(qChatSessionModel.creatorId.eq(userId)) // 只关联当前用户的会话
                )
                .leftJoin(qChatAnswerModel).on(
                    qChatAnswerModel.sessionId.eq(qChatSessionModel.id) // 关联回答表
                )
                .leftJoin(qRobotKnowledgeModel).on(qRobotModel.id.eq(qRobotKnowledgeModel.robotId))
                .where(builder)
                .groupBy(qRobotModel.id, qRobotModel.robotName, qRobotModel.welcomeMessage, qRobotModel.remark,
                        qRobotModel.systemPrompt, qRobotModel.dialogueExamples, qRobotModel.searchMode,
                        qRobotModel.answerMode, qRobotModel.similarityThreshold, qRobotModel.maxHits,qRobotKnowledgeModel.answerStrategy)
                .orderBy(
                    // 按用户最后回答时间倒序，如果没有回答则使用机器人创建时间
                    qChatAnswerModel.createTime.max().coalesce(qRobotModel.createTime).desc()
                )
                .fetch();

        return result;
    }

    @Override
    public Map<String, List<ChatSessionResp>> searchAllSession(ChatSessionSearchReq chatSessionSearchReq) {
        log.info("查询用户会话列表: robotId={}, robotName={}, userId={}, keyword={}",
                chatSessionSearchReq.getRobotId(), chatSessionSearchReq.getRobotName(),
                LoginContextHolder.getLoginUserId(), chatSessionSearchReq.getKeyword());

        QChatSessionModel qChatSessionModel = QChatSessionModel.chatSessionModel;
        QChatAnswerModel qChatAnswerModel = QChatAnswerModel.chatAnswerModel;
        QRobotModel qRobotModel = QRobotModel.robotModel;

        BooleanBuilder builder = new BooleanBuilder();
        // 用户隔离：只查询当前用户的会话
        builder.and(qChatSessionModel.creatorId.eq(LoginContextHolder.getLoginUserId()));

        // 机器人ID过滤（可选）
        if (chatSessionSearchReq.getRobotId() != null) {
            builder.and(qChatSessionModel.robotId.eq(chatSessionSearchReq.getRobotId()));
        }

        // 会话标题关键词搜索（可选）
        if(StrUtil.isNotBlank(chatSessionSearchReq.getKeyword())){
            builder.and(qChatSessionModel.title.contains(chatSessionSearchReq.getKeyword()));
        }

        // 机器人名称关键词搜索（可选）
        if(StrUtil.isNotBlank(chatSessionSearchReq.getRobotName())){
            builder.and(qRobotModel.robotName.contains(chatSessionSearchReq.getRobotName()));
        }

        // 使用左连接获取会话、机器人信息和最新回答时间，然后按最新活动时间排序
        List<ChatSessionResp> sessionList = queryFactory.select(Projections.bean(ChatSessionResp.class,
                        qChatSessionModel.id,
                        qChatSessionModel.title,
                        qChatSessionModel.chatType,
                        qChatSessionModel.createTime,
                        // 计算最新活动时间：最新回答时间或会话创建时间
                        qChatAnswerModel.createTime.max().coalesce(qChatSessionModel.createTime).as("lastActivityTime")))
                .from(qChatSessionModel)
                .leftJoin(qRobotModel).on(qChatSessionModel.robotId.eq(qRobotModel.id))
                .leftJoin(qChatAnswerModel).on(qChatAnswerModel.sessionId.eq(qChatSessionModel.id))
                .where(builder)
                .groupBy(qChatSessionModel.id, qChatSessionModel.title, qChatSessionModel.chatType, qChatSessionModel.createTime)
                .orderBy(
                    // 使用最新回答时间，如果没有回答则使用会话创建时间
                    qChatAnswerModel.createTime.max().coalesce(qChatSessionModel.createTime).desc()
                )
                .fetch();

        log.info("查询到{}个会话", sessionList.size());

        // 按日期分组，使用中文key
        Map<String, List<ChatSessionResp>> result = groupSessionsByDateWithChineseKeys(sessionList);

//        log.info("会话按日期分组完成: {}", result.keySet().stream()
//                .map(key -> key + "(" + result.get(key).size() + ")")
//                .reduce((a, b) -> a + ", " + b).orElse("无"));

        return result;
    }

    @Override
    public void thumbsUp(Long id) {
        updateAnswerThumbs(id, Boolean.TRUE);
    }

    @Override
    public void thumbsDown(Long id) {
        updateAnswerThumbs(id, Boolean.FALSE);
    }

    @Override
    public List<ChatHistoryResp> searchSessionHistory(Long sessionId) {
        log.info("查询会话历史详情: sessionId={}, userId={}", sessionId, LoginContextHolder.getLoginUserId());

        // 1. 验证会话权限
        validateSessionOwnership(sessionId);

        // 2. 查询问题和回答（按创建时间升序）
        List<ChatQuestionModel> questionModelList = chatQuestionRepository.findBySessionIdOrderByCreateTimeAsc(sessionId);
        List<ChatAnswerModel> answerModelList = chatAnswerRepository.findBySessionIdOrderByCreateTimeAsc(sessionId);

        log.debug("查询到问题数量: {}, 回答数量: {}", questionModelList.size(), answerModelList.size());

        // 3. 构建回答映射（questionId -> ChatAnswerModel）
        Map<Long, ChatAnswerModel> answerMap = answerModelList.stream()
                .collect(Collectors.toMap(ChatAnswerModel::getQuestionId, a -> a));

        // 4. 查询所有回答的事件
        Map<Long, List<ChatAnswerEventResp>> answerEventsMap = getAnswerEventsMap(answerModelList);

        // 5. 遍历问题构建响应结果
        List<ChatHistoryResp> result = new ArrayList<>();
        for (ChatQuestionModel questionModel : questionModelList) {
            ChatHistoryResp historyResp = buildChatHistoryRespFromQuestion(questionModel, answerMap, answerEventsMap, sessionId);
            if (historyResp != null) {
                result.add(historyResp);
            }
        }

        log.info("会话历史详情查询完成: sessionId={}, 对话数={}", sessionId, result.size());
        return result;
    }

    @Override
    public List<ChatHistoryResp> searchLastSessionHistoryByRobotId(Long robotId) {
        log.info("查询机器人最后一次对话历史详情: robotId={}, userId={}", robotId, LoginContextHolder.getLoginUserId());

        // 1. 查询该机器人最后一次会话
        QChatSessionModel qChatSessionModel = QChatSessionModel.chatSessionModel;
        QChatAnswerModel qChatAnswerModel = QChatAnswerModel.chatAnswerModel;

        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qChatSessionModel.robotId.eq(robotId));
        builder.and(qChatSessionModel.creatorId.eq(LoginContextHolder.getLoginUserId()));

        // 查询最后一次会话（按最新活动时间排序，取第一个）
        Long lastSessionId = queryFactory.select(qChatSessionModel.id)
                .from(qChatSessionModel)
                .leftJoin(qChatAnswerModel).on(qChatAnswerModel.sessionId.eq(qChatSessionModel.id))
                .where(builder)
                .groupBy(qChatSessionModel.id, qChatSessionModel.createTime)
                .orderBy(
                    // 按最新活动时间降序排列，取最新的一个
                    qChatAnswerModel.createTime.max().coalesce(qChatSessionModel.createTime).desc()
                )
                .limit(1)
                .fetchFirst();

        if (lastSessionId == null) {
            log.info("机器人{}没有找到任何会话记录", robotId);
            return new ArrayList<>();
        }

        log.info("找到机器人{}的最后一次会话: sessionId={}", robotId, lastSessionId);

        // 2. 调用现有的查询会话历史详情方法
        return searchSessionHistory(lastSessionId);
    }

    private void updateAnswerThumbs(Long id, Boolean thumbs) {
        Optional<ChatAnswerModel> optional = chatAnswerRepository.findById(id);
        if (!optional.isPresent()) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "回答不存在！");
        }
        ChatAnswerModel chatAnswerModel = optional.get();
        chatAnswerModel.setThumbsUp(thumbs);
        chatAnswerRepository.save(chatAnswerModel);
    }


    private void handleException(SseEmitter emitter, String errorTrace) {
        try {
            emitter.send(
                    SseEmitter.event()
                            .name("error")
                            .data(
                                    CommonResult.error(
                                            OUTER_SERVER_EXCEPTION.getCode(), CHAT_DEFAULT_ERROR_MESSAGE, errorTrace)));
            emitter.complete();
        } catch (ClientAbortException clientAbortException) {
            log.error("连接关闭失败！", clientAbortException);
        } catch (IOException e) {
            log.error("处理异常", e);
        }
    }

    private boolean checkQuestionSpam(String id, String content) {
        if (StringUtils.isEmpty(content)) {
            return false;
        }
        try {
            Boolean antispam = antispamUtil.checkAntispam(id, content);
            return (antispam != null && antispam);
        } catch (Exception e) {
            log.error("检查问题敏感词异常，id: {}, content length: {}, error: {}",
                    id, content.length(), e.getMessage(), e);
            // 异常时返回false，不影响正常对话流程
            return false;
        }
    }

    public boolean checkAnswerSpam(String answer) {
        try {
            Boolean aBoolean = antispamUtil.checkAntispam(snowflake.nextIdStr(), answer);
            return aBoolean != null && aBoolean;
        } catch (Exception e) {
            log.error("检查回答敏感词异常，answer length: {}, error: {}",
                    answer != null ? answer.length() : 0, e.getMessage(), e);
            // 异常时返回false，不影响正常对话流程
            return false;
        }
    }

    private void handleSpamResponse(SseEmitter emitter, String message) {
        try {
            emitter.send(SseEmitter.event().name("spam")
                    .data(message));
            emitter.complete();
        } catch (IOException e) {
            log.error("发送敏感词响应失败，连接可能已关闭: {}", e.getMessage(), e);
            // 不再调用handleException，避免重复complete导致socket closed异常
            try {
                emitter.complete();
            } catch (Exception ignore) {
                // 忽略complete时的异常，连接可能已经关闭
            }
        }
    }

    private boolean checkQuestionSpam(String question, ChatVO chatVO, boolean isSimulate, SseEmitter emitter) {
        try {
            Boolean aBoolean = antispamUtil.checkAntispam(snowflake.nextIdStr(), question);
            if (aBoolean != null && aBoolean) {
                // 给出默认答案
                String answer = "对不起，这个问题超出了我可以处理的范围。您能否提出其他类型的问题？";
                handleSpamResponse(emitter, answer);
                chatVO.setQuestionId(snowflake.nextId());
                saveQuestionAndAnswerAndSession(chatVO, answer, isSimulate);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("检查问题敏感词异常，question length: {}, error: {}",
                    question != null ? question.length() : 0, e.getMessage(), e);
            // 异常时返回false，不影响正常对话流程
            return false;
        }
    }


    private Long checkRobot(Long robotId) {
        if (null == robotId) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "机器人ID！");
        }
        Optional<RobotModel> optional = robotRepository.findById(robotId);
        if (optional.isPresent()) {
            RobotModel robot = optional.get();
            // 移除状态检查，无论激活还是未激活都可以使用
            Long modelGroupId = robot.getModelGroupId();
            log.info("机器人检查完成: robotId={}, robotName={}, modelGroupId={}",
                    robotId, robot.getRobotName(), modelGroupId);
            return modelGroupId;
        } else {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "机器人不存在！");
        }
    }

    /**
     * 获取机器人的答案策略
     */
    private AnswerStrategyEnum getRobotAnswerStrategy(Long robotId) {
        QRobotKnowledgeModel qRobotKnowledgeModel = QRobotKnowledgeModel.robotKnowledgeModel;

        RobotKnowledgeModel robotKnowledgeModel = queryFactory.selectFrom(qRobotKnowledgeModel)
                .where(qRobotKnowledgeModel.robotId.eq(robotId))
                .fetchFirst();

        if (robotKnowledgeModel != null && robotKnowledgeModel.getAnswerStrategy() != null) {
            return robotKnowledgeModel.getAnswerStrategy();
        }

        // 默认使用知识库策略
        log.warn("机器人 {} 未配置答案策略，使用默认知识库策略", robotId);
        return AnswerStrategyEnum.KNOWLEDGE_BASE;
    }

    /**
     * 添加可选参数到请求参数中
     */
    private void addOptionalParams(HashMap<String, Object> param, Integer enableModelRouter, Integer enableReasoning) {
        // 只有当参数不为null时才添加到请求中
        if (enableModelRouter != null) {
            param.put("enable_model_router", enableModelRouter);
        }
        if (enableReasoning != null) {
            param.put("enable_reasoning", enableReasoning);
        }
    }

    /**
     * 将文件ID插入到question表中（用于文件策略）
     * 检查重复，避免同一会话中重复插入相同的文件ID
     *
     * @param sessionId 会话ID
     * @param fileIds 文件ID列表
     * @param isSimulate 是否模拟模式
     */
    private void insertFileIdsToQuestionTable(Long sessionId, List<String> fileIds, Boolean isSimulate) {
        if (isSimulate) {
            log.info("模拟模式下不插入文件ID到question表");
            return;
        }

        log.info("开始检查并插入{}个文件ID到question表，sessionId: {}", fileIds.size(), sessionId);

        // 1. 查询当前会话中已存在的文件ID
        Set<String> existingFileIds = getExistingFileIdsInSession(sessionId);
        log.info("当前会话{}中已存在{}个文件ID", sessionId, existingFileIds.size());

        // 2. 过滤出需要插入的文件ID（去除重复）
        List<String> newFileIds = new ArrayList<>();
        for (String fileId : fileIds) {
            if (!existingFileIds.contains(fileId)) {
                newFileIds.add(fileId);
            } else {
                log.debug("文件ID已存在，跳过插入: fileId={}, sessionId={}", fileId, sessionId);
            }
        }

        // 3. 插入新的文件ID
        if (newFileIds.isEmpty()) {
            log.info("所有文件ID都已存在，无需插入新记录");
            return;
        }

        log.info("需要插入{}个新文件ID到question表", newFileIds.size());
        for (String fileId : newFileIds) {
            ChatQuestionModel fileQuestionModel = new ChatQuestionModel();
            fileQuestionModel.setId(snowflake.nextId());
            fileQuestionModel.setContent(fileId);
            fileQuestionModel.setSessionId(sessionId);
            fileQuestionModel.setEventType(EventTypeEnum.FILE);

            chatQuestionRepository.save(fileQuestionModel);
            log.debug("插入新文件ID到question表: fileId={}, sessionId={}", fileId, sessionId);
        }

        log.info("完成插入{}个新文件ID到question表，跳过{}个重复文件ID",
                newFileIds.size(), fileIds.size() - newFileIds.size());
    }

    /**
     * 获取当前会话中已存在的文件ID集合
     *
     * @param sessionId 会话ID
     * @return 已存在的文件ID集合
     */
    private Set<String> getExistingFileIdsInSession(Long sessionId) {
        QChatQuestionModel qChatQuestionModel = QChatQuestionModel.chatQuestionModel;

        List<String> existingFileIds = queryFactory
                .select(qChatQuestionModel.content)
                .from(qChatQuestionModel)
                .where(qChatQuestionModel.sessionId.eq(sessionId)
                        .and(qChatQuestionModel.eventType.eq(EventTypeEnum.FILE)))
                .fetch();

        return new HashSet<>(existingFileIds);
    }

    /**
     * 保存对话内容并生成历史session
     *
     * @param chatVO
     * @param answer
     */
    public void saveQuestionAndAnswerAndSession(ChatVO chatVO, String answer, Boolean isSimulate) {
        log.info("模型回答：" + answer);
        if (isSimulate) {
            return;
        }
        ChatQuestionModel chatQuestionModel = new ChatQuestionModel();
        chatQuestionModel.setId(chatVO.getQuestionId());
        chatQuestionModel.setContent(chatVO.getQuestion());
        chatQuestionModel.setSessionId(chatVO.getSessionId());
        chatQuestionRepository.save(chatQuestionModel);
        ChatAnswerModel chatAnswerModel;
        Optional<ChatAnswerModel> optionalChatAnswerModel = chatAnswerRepository.findById(chatVO.getAnswerId());
        if (optionalChatAnswerModel.isPresent()) {
//        如果回答能找到，说明是重新回答，不仅需要更新回答的内容，还要把点赞也清空
            chatAnswerModel = optionalChatAnswerModel.get();
            chatAnswerModel.setThumbsUp(null);
        } else {
            chatAnswerModel = new ChatAnswerModel();
            chatAnswerModel.setId(chatVO.getAnswerId());
            chatAnswerModel.setContent(answer);
            chatAnswerModel.setQuestionId(chatVO.getQuestionId());
            chatAnswerModel.setSessionId(chatVO.getSessionId());
        }
        chatAnswerModel.setContent(answer);
        chatAnswerRepository.save(chatAnswerModel);
        Optional<ChatSessionModel> optional = chatSessionRepository.findById(chatVO.getSessionId());
        if (!optional.isPresent()) {
            ChatSessionModel chatSessionModel = new ChatSessionModel();
            chatSessionModel.setId(chatVO.getSessionId());
            chatSessionModel.setRobotId(chatVO.getRobotId());
            chatSessionModel.setTitle(chatVO.getQuestion());
            chatSessionModel.setChatType(chatVO.getChatType());
            chatSessionRepository.save(chatSessionModel);
        }
    }

    /**
     * 保存问题、回答和所有事件
     *
     * @param chatVO 对话上下文
     * @param events 所有事件列表
     * @param isSimulate 是否模拟模式
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveQuestionAndAnswerWithEvents(ChatVO chatVO, List<ParsedEvent> events, Boolean isSimulate) {
        if (isSimulate) {
            return;
        }
        
        // 提取MESSAGE类型的内容作为主要回答（保留完整内容，包含思考标签）
        StringBuilder messageBuilder = new StringBuilder();
        for (ParsedEvent event : events) {
            if (event.getEventType() == ParsedEventType.MESSAGE && event.getContent() != null) {
                messageBuilder.append(event.getContent());
            }
        }
        
        log.info("保存对话，消息内容：{}, 事件总数：{}", messageBuilder.toString(), events.size());
        
        // 保存问题
        ChatQuestionModel chatQuestionModel = new ChatQuestionModel();
        chatQuestionModel.setId(chatVO.getQuestionId());
        chatQuestionModel.setContent(chatVO.getQuestion());
        chatQuestionModel.setSessionId(chatVO.getSessionId());
        chatQuestionModel.setEventType(EventTypeEnum.MESSAGE);
        chatQuestionRepository.save(chatQuestionModel);
        
        // 保存主答案记录
        ChatAnswerModel chatAnswerModel;
        Optional<ChatAnswerModel> optionalChatAnswerModel = chatAnswerRepository.findById(chatVO.getAnswerId());
        if (optionalChatAnswerModel.isPresent()) {
            // 重新回答时清空之前的事件
            chatAnswerEventRepository.deleteByAnswerId(chatVO.getAnswerId());
            chatAnswerModel = optionalChatAnswerModel.get();
            chatAnswerModel.setThumbsUp(null);
        } else {
            chatAnswerModel = new ChatAnswerModel();
            chatAnswerModel.setId(chatVO.getAnswerId());
            chatAnswerModel.setQuestionId(chatVO.getQuestionId());
            chatAnswerModel.setSessionId(chatVO.getSessionId());
        }
        chatAnswerModel.setContent(messageBuilder.toString());
        chatAnswerRepository.save(chatAnswerModel);
        
        // 保存所有事件详情
        saveAnswerEvents(chatVO.getAnswerId(), events);
        
        // 保存会话信息
        Optional<ChatSessionModel> optional = chatSessionRepository.findById(chatVO.getSessionId());
        if (!optional.isPresent()) {
            ChatSessionModel chatSessionModel = new ChatSessionModel();
            chatSessionModel.setId(chatVO.getSessionId());
            chatSessionModel.setRobotId(chatVO.getRobotId());
            chatSessionModel.setTitle(chatVO.getQuestion());
            chatSessionModel.setChatType(chatVO.getChatType());
            chatSessionRepository.save(chatSessionModel);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTitle(UpdateTitleReq updateTitleReq) {
        // 1. 检查sessionId是否存在
        ChatSessionModel chatSessionModel = chatSessionRepository.findById(updateTitleReq.getSessionId()).orElseThrow(
                () -> ServiceExceptionUtil.exception(OBJECT_NOT_EXISTED, "对话不存在"));

        // 2. 更新会话标题
        chatSessionModel.setTitle(updateTitleReq.getTitle());
        chatSessionRepository.save(chatSessionModel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSession(Long sessionId) {
        log.info("开始删除会话: sessionId={}, userId={}", sessionId, LoginContextHolder.getLoginUserId());

        // 1. 验证会话是否存在且属于当前用户
        ChatSessionModel chatSession = validateSessionOwnership(sessionId);

        // 2. 查询会话下的所有回答ID（用于删除回答事件）
        List<Long> answerIds = getAnswerIdsBySessionId(sessionId);
        log.info("会话{}包含{}个回答", sessionId, answerIds.size());

        // 3. 删除回答事件（必须先删除，因为有外键约束）
        if (!answerIds.isEmpty()) {
            chatAnswerEventRepository.deleteByAnswerIdIn(answerIds);
        }

        // 4. 删除回答记录
        chatAnswerRepository.deleteBySessionId(sessionId);

        // 5. 删除问题记录
        chatQuestionRepository.deleteBySessionId(sessionId);

        // 6. 删除会话记录
        chatSessionRepository.deleteById(sessionId);

        log.info("会话删除完成: sessionId={}, robotId={}, title={}",
                sessionId, chatSession.getRobotId(), chatSession.getTitle());
    }

    /**
     * 保存回答的所有事件详情
     */
    private void saveAnswerEvents(Long answerId, List<ParsedEvent> events) {
        for (int i = 0; i < events.size(); i++) {
            ParsedEvent event = events.get(i);
            
            ChatAnswerEventModel eventModel = new ChatAnswerEventModel();
            eventModel.setAnswerId(answerId);
            eventModel.setEventType(event.getEventType().name());
            eventModel.setEventContent(event.getContent());
            eventModel.setSequenceOrder(i + 1);
            eventModel.setIncludeInContext(shouldIncludeInContext(event.getEventType()));
            eventModel.setEventStatus(EventStatusEnum.SUCCESS.getCode());
            
            chatAnswerEventRepository.save(eventModel);
        }
    }

    /**
     * 判断事件类型是否应该参与上下文传递
     */
    private boolean shouldIncludeInContext(ParsedEventType eventType) {
        return eventType == ParsedEventType.MESSAGE; // 只有MESSAGE参与上下文
    }

    /**
     * 移除思考标签 <think>...</think> 中的内容
     * 支持多行和嵌套的思考标签
     */
    private String removeThinkTags(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }

        // 使用正则表达式移除 <think>...</think> 标签及其内容
        // (?s) 表示 DOTALL 模式，让 . 匹配换行符
        // .*? 表示非贪婪匹配
        String result = content.replaceAll("(?s)<think>.*?</think>", "");

        // 清理可能产生的多余空行
        result = result.replaceAll("\n\n+", "\n\n");

        log.debug("思考标签过滤: 原长度={}, 过滤后长度={}", content.length(), result.length());
        return result;
    }

    /**
     * 将会话列表按日期分组（使用中文key）
     * @param sessionList 会话列表
     * @return 按日期分组的会话Map，key为中文描述
     */
    private Map<String, List<ChatSessionResp>> groupSessionsByDateWithChineseKeys(List<ChatSessionResp> sessionList) {
        log.debug("开始按日期分组会话（中文key），总数: {}", sessionList.size());

        Map<String, List<ChatSessionResp>> result = new LinkedHashMap<>();

        // 初始化所有分组，保证顺序（使用中文描述作为key）
        for (SessionDateGroupEnum group : SessionDateGroupEnum.values()) {
            result.put(group.getDescription(), new ArrayList<>());
        }

        // 按日期分组
        for (ChatSessionResp session : sessionList) {
            SessionDateGroupEnum dateGroup = SessionDateGroupEnum.getDateGroup(session.getLastActivityTime());
            String chineseKey = dateGroup.getDescription();
            result.get(chineseKey).add(session);

            log.debug("会话分组: sessionId={}, title={}, lastActivityTime={}, group={}",
                    session.getId(), session.getTitle(), session.getLastActivityTime(), chineseKey);
        }

        // 移除空分组
        result.entrySet().removeIf(entry -> entry.getValue().isEmpty());

        // 对每个分组内的会话按最新活动时间排序（降序）
        result.values().forEach(sessions ->
            sessions.sort((s1, s2) -> {
                if (s1.getLastActivityTime() == null && s2.getLastActivityTime() == null) {
                    return 0;
                } else if (s1.getLastActivityTime() == null) {
                    return 1;
                } else if (s2.getLastActivityTime() == null) {
                    return -1;
                } else {
                    return s2.getLastActivityTime().compareTo(s1.getLastActivityTime());
                }
            })
        );

        log.debug("会话按日期分组完成（中文key），非空分组数: {}", result.size());
        return result;
    }

    /**
     * 验证会话所有权
     * @param sessionId 会话ID
     * @return 会话实体
     * @throws RuntimeException 如果会话不存在或无权限访问
     */
    private ChatSessionModel validateSessionOwnership(Long sessionId) {
        if (sessionId == null) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "会话ID不能为空");
        }

        ChatSessionModel chatSession = chatSessionRepository.findById(sessionId)
                .orElseThrow(() -> ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "会话不存在"));

        // 验证会话是否属于当前用户
        Long currentUserId = LoginContextHolder.getLoginUserId();
        if (!currentUserId.equals(chatSession.getCreatorId())) {
            throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.FORBIDDEN, "无权限访问此会话");
        }

        log.debug("会话权限验证通过: sessionId={}, userId={}, robotId={}",
                sessionId, currentUserId, chatSession.getRobotId());
        return chatSession;
    }

    /**
     * 获取会话下的所有回答ID
     * @param sessionId 会话ID
     * @return 回答ID列表
     */
    private List<Long> getAnswerIdsBySessionId(Long sessionId) {
        QChatAnswerModel qChatAnswerModel = QChatAnswerModel.chatAnswerModel;

        List<Long> answerIds = queryFactory
                .select(qChatAnswerModel.id)
                .from(qChatAnswerModel)
                .where(qChatAnswerModel.sessionId.eq(sessionId))
                .fetch();

        log.debug("查询到会话{}的回答ID列表: {}", sessionId, answerIds);
        return answerIds;
    }

    /**
     * 获取回答事件映射
     * @param answerModelList 回答模型列表
     * @return answerId -> 事件列表的映射
     */
    private Map<Long, List<ChatAnswerEventResp>> getAnswerEventsMap(List<ChatAnswerModel> answerModelList) {
        if (answerModelList.isEmpty()) {
            return new HashMap<>();
        }

        // 提取所有回答ID
        List<Long> answerIds = answerModelList.stream()
                .map(ChatAnswerModel::getId)
                .collect(Collectors.toList());

        log.debug("查询{}个回答的事件信息", answerIds.size());

        // 查询所有事件（按answerId和sequenceOrder排序）
        List<ChatAnswerEventModel> allEvents = chatAnswerEventRepository
                .findByAnswerIdInOrderByAnswerIdAscSequenceOrderAsc(answerIds);

        log.debug("查询到{}个回答事件", allEvents.size());

        // 按answerId分组事件
        Map<Long, List<ChatAnswerEventResp>> answerEventsMap = new HashMap<>();
        for (ChatAnswerEventModel eventModel : allEvents) {
            Long answerId = eventModel.getAnswerId();
            answerEventsMap.computeIfAbsent(answerId, k -> new ArrayList<>())
                    .add(OrikaUtils.convert(eventModel, ChatAnswerEventResp.class));
        }

        return answerEventsMap;
    }

    /**
     * 从问题构建单个对话历史响应对象
     * @param questionModel 问题模型
     * @param answerMap 回答映射（questionId -> ChatAnswerModel）
     * @param answerEventsMap 回答事件映射
     * @param sessionId 会话ID
     * @return 对话历史响应对象
     */
    private ChatHistoryResp buildChatHistoryRespFromQuestion(ChatQuestionModel questionModel,
                                                            Map<Long, ChatAnswerModel> answerMap,
                                                            Map<Long, List<ChatAnswerEventResp>> answerEventsMap,
                                                            Long sessionId) {

        // 查找对应的回答
        ChatAnswerModel answerModel = answerMap.get(questionModel.getId());
        if (answerModel == null) {
//            如果没有找到对应的Answer，则构造一个空的Answer对象，比如：用户上传文件，则没有对应的Answer
            answerModel = new ChatAnswerModel();
        }

        ChatHistoryResp historyResp = new ChatHistoryResp();

        // 设置问题基础信息
        ChatQuestionBase questionBase = OrikaUtils.convert(questionModel, ChatQuestionBase.class);

        // 如果是文件类型问题，补充文件信息
        if (questionModel.getEventType() == EventTypeEnum.FILE) {
            enrichFileInfo(questionBase, questionModel.getContent(), sessionId);
        }

        historyResp.setChatQuestionBase(questionBase);

        // 设置回答基础信息
        historyResp.setChatAnswerBase(OrikaUtils.convert(answerModel, ChatAnswerBase.class));

        // 设置回答事件列表（已按sequence_order排序）
        List<ChatAnswerEventResp> events = answerEventsMap.getOrDefault(answerModel.getId(), new ArrayList<>());
        historyResp.setAnswerEvents(events);

        log.debug("构建历史记录: questionId={}, answerId={}, eventCount={}, questionType={}",
                questionModel.getId(), answerModel.getId(), events.size(), questionModel.getEventType());

        return historyResp;
    }

    /**
     * 为文件类型问题补充文件信息
     * @param questionBase 问题基础对象
     * @param fileIdStr 文件ID字符串
     * @param sessionId 会话ID
     */
    private void enrichFileInfo(ChatQuestionBase questionBase, String fileIdStr, Long sessionId) {
        try {
            Long fileId = Long.valueOf(fileIdStr);
            Optional<FileModel> fileOptional = fileRepository.findById(fileId);

            if (fileOptional.isPresent()) {
                FileModel fileModel = fileOptional.get();
                FileInfo fileInfo = new FileInfo();
                fileInfo.setFileId(fileId);
                fileInfo.setFileName(fileModel.getFileName());
                fileInfo.setHdfsPath(fileModel.getHdfsPath());

                // 构建PDF预览路径
                if (fileModel.getPdfHdfsPath() != null) {
                    fileInfo.setPreviewPDFPath(fileComponent.previewUrlByPath(fileModel.getPdfHdfsPath()));
                }

                questionBase.setFileInfo(fileInfo);
                log.debug("补充文件信息: fileId={}, fileName={}", fileId, fileModel.getFileName());
            } else {
                log.error("未找到文件信息: sessionId={}, fileId={}", sessionId, fileIdStr);
            }
        } catch (NumberFormatException e) {
            log.error("文件ID格式错误: sessionId={}, fileIdStr={}", sessionId, fileIdStr, e);
        }
    }

    private HashMap<String, Object> buildChatParamsForDB(Long modelGroupId,List<Long> dataSourceIds,String question,AnswerStrategyEnum answerStrategy) {

        // 1. 收集数据库配置信息
         QDataSourceModel qDataSourceModel = QDataSourceModel.dataSourceModel;
        QDataSourceExtensionModel qDataSourceExtensionModel = QDataSourceExtensionModel.dataSourceExtensionModel;

        List<DataBaseTableInfo> dataBaseTableInfoList = queryFactory.select(
                        Projections.bean(DataBaseTableInfo.class,
                                qDataSourceExtensionModel.ip,
                                qDataSourceExtensionModel.port,
                                qDataSourceExtensionModel.databaseName,
                                qDataSourceExtensionModel.userName,
                                qDataSourceExtensionModel.password,
                                qDataSourceExtensionModel.tableName,
                                qDataSourceModel.datasourceType))
                .from(qDataSourceExtensionModel)
                .join(qDataSourceModel).on(qDataSourceExtensionModel.datasourceId.eq(qDataSourceModel.id))
                .where(qDataSourceModel.id.in(dataSourceIds))
                .fetch();

        // 2. 构建基础参数
        HashMap<String, Object> param = buildBaseParams(question);

        // 3. 添加模型组配置
        addModelGroupParams(param,answerStrategy,modelGroupId);

        // 4. 添加数据库配置（使用db_configs格式）
        List<Map<String, Object>> dbConfigs = dataBaseServiceForAnswerStrategy.buildDatabaseConfigs(dataBaseTableInfoList);
        param.put("db_configs", dbConfigs);

        log.info("数据库策略参数构建完成，db_configs数量: {}", dbConfigs.size());
        return param;
    }
    private HashMap<String, Object> buildChatParamsForMCP(Long modelGroupId,List<Long> mcpIds,String question,AnswerStrategyEnum answerStrategy) {

        // 1. 收集MCP配置信息
        QMcpModel qMcpModel = QMcpModel.mcpModel;

        // 1.1 查询机器人关联的MCP服务
        List<com.querydsl.core.Tuple> mcpTuples = jpaQueryFactory
                .select(qMcpModel.serverName, qMcpModel.serverUrl, qMcpModel.mcpDesc)
                .from(qMcpModel)
                .where(qMcpModel.id.in(mcpIds))
                .fetch();

        // 1.2 转换为dynamic_servers格式
        List<Map<String, Object>> dynamicServers = new ArrayList<>();
        for (com.querydsl.core.Tuple tuple : mcpTuples) {
            Map<String, Object> server = new HashMap<>();
            server.put("server_key", tuple.get(qMcpModel.serverName));
            server.put("url", tuple.get(qMcpModel.serverUrl));
            server.put("description", tuple.get(qMcpModel.mcpDesc));
            server.put("make_default", false);
            dynamicServers.add(server);
        }

        HashMap<String, Object> param = buildBaseParams(question);

        // 2. MCP策略特有参数（使用query字段而不是question）
        param.remove("question");
        param.put("query", question); // MCP策略使用query字段

        // 3. 添加模型组配置
        addModelGroupParams(param,answerStrategy,modelGroupId);

        // 4. 添加MCP特有参数
        param.put("dynamic_servers", dynamicServers);
        param.put("max_tool_rounds", 10); // 默认最大工具调用轮次
        return param;
    }

    private HashMap<String, Object> buildChatParamsForFile(Long modelGroupId,List<Long> fileIds,String question,AnswerStrategyEnum answerStrategy,String userCustomPrompt) {

        // 1. 构建基础参数
        HashMap<String, Object> param = buildBaseParams(question);

        // 2. 文件策略特有参数
        param.put("file_ids", fileIds);
        param.put("streaming", 1); // 统一使用1而不是true
        // user_custom_prompt需要从数据库查询，这里先设置为null，由调用方设置
        param.put("user_custom_prompt", null);
        // 3. 添加模型组配置
        addModelGroupParams(param,answerStrategy,modelGroupId);

        // 4. 查询机器人的systemPrompt作为user_custom_prompt
        param.put("user_custom_prompt", userCustomPrompt);

        log.info("文件策略参数构建完成，db_configs数量: {}", param.size());
        return param;
    }

    private HashMap<String, Object> buildChatParamsForKB(Long modelGroupId, List<Long> kbIds, String question, AnswerStrategyEnum answerStrategy, Long robotId
        , SearchModeEnum searchMode, AnswerModeEnum answerMode, Integer topK, BigDecimal scoreThreshold) {

        // 1. 构建基础参数
        HashMap<String, Object> param = buildBaseParams(question);

        // 2. 知识库策略特有参数
        List<Map<String, String>> tags = chatTagService.getTagsFromRobot(robotId, jpaQueryFactory);
        List<String> aiKbids = new ArrayList<>();
        List<String> fileIds = new ArrayList<>();
        QKnowledgeBaseModel qKnowledgeBaseModel = QKnowledgeBaseModel.knowledgeBaseModel;
        QKnowledgeModel qKnowledgeModel = QKnowledgeModel.knowledgeModel;

        List<Tuple> tupleList = queryFactory
                .select(qKnowledgeBaseModel.aiFilebId, qKnowledgeModel.fileId)
                .from(qKnowledgeModel)
                .join(qKnowledgeBaseModel).on(qKnowledgeModel.kbId.eq(qKnowledgeBaseModel.id))
                .where(qKnowledgeBaseModel.id.in(kbIds))
                .fetch();
        Iterator<Tuple> iterator = tupleList.iterator();
        while (iterator.hasNext()) {
            Tuple tuple = iterator.next();
            if (tuple.get(qKnowledgeBaseModel.aiFilebId) != null) {
                aiKbids.add(String.valueOf(tuple.get(qKnowledgeBaseModel.aiFilebId)));
            }
            if (tuple.get(qKnowledgeModel.fileId) != null) {
                fileIds.add(String.valueOf(tuple.get(qKnowledgeModel.fileId)));
            }
        }
        param.put("kb_ids", aiKbids);
        param.put("user_id", "zyx"); // 知识库策略固定用户ID
        param.put("streaming", 1);
        param.put("tags_list", tags);
        param.put("file_ids",fileIds);
        // 3. 添加模型组配置
        addModelGroupParams(param,answerStrategy,modelGroupId);

        // 4. 添加知识库策略专用的机器人配置参数
        param.put("search_mode", Integer.valueOf(searchMode.getDescription()));
        param.put("answer_mode", Integer.valueOf(answerMode.getDescription()));
        param.put("top_k", topK);
        param.put("score_threshold", scoreThreshold);
        log.info("知识库策略参数构建完成，db_configs数量: {}", param.size());
        return param;
    }


    private HashMap<String, Object> buildBaseParams(String question) {
        HashMap<String, Object> param = new HashMap<>();

        // 数据库策略特有参数（使用query字段而不是question）
        param.put("query", question); // 数据库策略使用query字段
        // 真正的通用基础参数（所有策略都需要的参数）
        param.put("user_token", String.valueOf(LoginContextHolder.getLoginUserId()));
        param.put("history", null); // 由调用方补充

        return param;
    }

    private void addModelGroupParams(HashMap<String, Object> param, AnswerStrategyEnum answerStrategy,Long modelGroupId){
        AnswerStrategyService answerStrategyService = answerStrategyFactory.getAnswerStrategyService(answerStrategy);
        answerStrategyService.addModelGroupParams(param, modelGroupId);
    }

    @Override
    public PageResult<UserChatSessionPageResp> pageUserChatSessions(Integer pageNo, Integer pageSize, String keyword, String robotName) {
        log.info("分页查询用户历史会话: userId={}, pageNo={}, pageSize={}, keyword={}, robotName={}",
                LoginContextHolder.getLoginUserId(), pageNo, pageSize, keyword, robotName);

        QChatSessionModel qChatSessionModel = QChatSessionModel.chatSessionModel;
        QChatAnswerModel qChatAnswerModel = QChatAnswerModel.chatAnswerModel;
        QRobotModel qRobotModel = QRobotModel.robotModel;

        // 构建查询条件
        BooleanBuilder builder = new BooleanBuilder();
        builder.and(qChatSessionModel.creatorId.eq(LoginContextHolder.getLoginUserId()));

        // 会话标题关键词搜索
        if (StrUtil.isNotBlank(keyword)) {
            builder.and(qChatSessionModel.title.contains(keyword));
        }

        // 机器人名称关键词搜索
        if (StrUtil.isNotBlank(robotName)) {
            builder.and(qRobotModel.robotName.contains(robotName));
        }

        // 构建分页参数
        Pageable pageable = PageRequest.of(pageNo - 1, pageSize);

        // 执行查询 - 使用左连接获取会话、机器人信息和最新回答时间
        JPQLQuery<UserChatSessionPageResp> jpqlQuery = queryFactory.select(Projections.bean(UserChatSessionPageResp.class,
                        qChatSessionModel.id,
                        qChatSessionModel.title,
                        qChatSessionModel.robotId,
                        qRobotModel.robotName,
                        qChatSessionModel.chatType,
                        qChatSessionModel.createTime,
                        // 计算最新活动时间：最新回答时间或会话创建时间
                        qChatAnswerModel.createTime.max().coalesce(qChatSessionModel.createTime).as("lastActivityTime"),
                        // 统计对话轮次数量
                        qChatAnswerModel.id.count().as("conversationCount")))
                .from(qChatSessionModel)
                .leftJoin(qRobotModel).on(qChatSessionModel.robotId.eq(qRobotModel.id))
                .leftJoin(qChatAnswerModel).on(qChatAnswerModel.sessionId.eq(qChatSessionModel.id))
                .where(builder)
                .groupBy(qChatSessionModel.id, qChatSessionModel.title, qChatSessionModel.robotId,
                        qRobotModel.robotName, qChatSessionModel.chatType, qChatSessionModel.createTime)
                .orderBy(
                    // 按最新活动时间降序排列
                    qChatAnswerModel.createTime.max().coalesce(qChatSessionModel.createTime).desc()
                )
                .offset(pageable.getOffset())
                .limit(pageable.getPageSize());

        // 获取总数
        Long total = jpqlQuery.fetchCount();

        // 获取分页数据
        List<UserChatSessionPageResp> resultList = jpqlQuery.fetch();

        log.info("分页查询用户历史会话完成: 总数={}, 当前页数据量={}", total, resultList.size());

        return PageResult.of(resultList, total);
    }
}

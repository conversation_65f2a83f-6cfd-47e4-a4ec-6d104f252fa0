package com.center.emergency.biz.datasource.service;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.center.emergency.biz.database.connector.DataConnectorFactory;
import com.center.emergency.biz.database.connector.DatabaseConnector;
import com.center.emergency.biz.datasource.persistence.*;
import com.center.emergency.biz.datasource.pojo.*;
import com.center.emergency.biz.robot.persitence.RobotKBRepository;
import com.center.emergency.common.enumeration.DataSourceEnum;
import com.center.framework.common.context.LoginContextHolder;
import com.center.framework.common.enumerate.SingleAndMultipleEnum;
import com.center.framework.common.exception.constant.GlobalErrorCodeConstants;
import com.center.framework.common.exception.util.ServiceExceptionUtil;
import com.center.framework.common.utils.object.OrikaUtils;
import com.center.framework.web.pojo.PageResult;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.*;

@Service
@Slf4j
public class DataSourceServiceImpl implements DataSourceService {

  @Resource private DataSourceRepository dataSourceRepository;

  @Resource private DataSourceExtensionRepository dataSourceExtensionRepository;

  @Resource private Snowflake snowflake;

  @Resource
  private JPAQueryFactory queryFactory;

  @Resource
  private RobotKBRepository robotKBRepository;

  @Override
  @Transactional
  public void createSingleDataSource(SingleDataSourceCreateReq singleDataSourceCreateReq) {
    checkDuplicateName(singleDataSourceCreateReq.getDataSourceBase().getDatasourceName());
    checkConnection(
        DataSourceEnum.MYSQL,
        singleDataSourceCreateReq.getDatabaseName(),
        singleDataSourceCreateReq.getIp(),
        singleDataSourceCreateReq.getPort(),
        singleDataSourceCreateReq.getUserName(),
        singleDataSourceCreateReq.getPassword(),
        singleDataSourceCreateReq.getJdbcParameter());

    DataSourceModel dataSourceModel =
        buildDataSourceMode(
            singleDataSourceCreateReq.getDataSourceBase().getDatasourceDescription(),
            singleDataSourceCreateReq.getDataSourceBase().getDatasourceName(),
            SingleAndMultipleEnum.SINGLE,
            singleDataSourceCreateReq.getDataSourceBase().getRelation());
    dataSourceRepository.save(dataSourceModel);
    dataSourceExtensionRepository.save(
        buildDataSourceExtensionModel(
            dataSourceModel.getId(),
            singleDataSourceCreateReq.getDatabaseName(),
            singleDataSourceCreateReq.getIp(),
            singleDataSourceCreateReq.getUserName(),
            singleDataSourceCreateReq.getPassword(),
            singleDataSourceCreateReq.getPort(),
            singleDataSourceCreateReq.getJdbcParameter(),
            StrUtil.EMPTY,
            StrUtil.EMPTY));
  }

  @Override
  @Transactional
  public void createMultipleDataSource(MultipleDataSourceCreateReq multipleDataSourceCreateReq) {
    checkDuplicateName(multipleDataSourceCreateReq.getDataSourceBase().getDatasourceName());
    DataSourceModel dataSourceModel =
        buildDataSourceMode(
            multipleDataSourceCreateReq.getDataSourceBase().getDatasourceDescription(),
            multipleDataSourceCreateReq.getDataSourceBase().getDatasourceName(),
            SingleAndMultipleEnum.MULTIPLE,
            multipleDataSourceCreateReq.getDataSourceBase().getRelation());
    dataSourceRepository.save(dataSourceModel);
    dataSourceExtensionRepository.saveAll(
        buildDataSourceModelList(
            multipleDataSourceCreateReq.getDataSourceExtensionBaseList(), dataSourceModel.getId()));
  }

  @Override
  @Transactional
  public void updateSingleDataSource(SingleDataSourceUpdateReq singleDataSourceUpdateReq) {
    checkConnection(DataSourceEnum.MYSQL,singleDataSourceUpdateReq.getDatabaseName(),singleDataSourceUpdateReq.getIp(),
            singleDataSourceUpdateReq.getPort(), singleDataSourceUpdateReq.getUserName(), singleDataSourceUpdateReq.getPassword(),
            singleDataSourceUpdateReq.getJdbcParameter());
    DataSourceModel dataSourceModel = getDataSourceModelById(singleDataSourceUpdateReq.getId());
    if (!dataSourceModel
        .getDatasourceName()
        .equals(singleDataSourceUpdateReq.getDataSourceBase().getDatasourceName())) {
      checkDuplicateName(singleDataSourceUpdateReq.getDataSourceBase().getDatasourceName());
    }
    dataSourceModel.setDatasourceName(
        singleDataSourceUpdateReq.getDataSourceBase().getDatasourceName());
    dataSourceModel.setDatasourceDescription(
        singleDataSourceUpdateReq.getDataSourceBase().getDatasourceDescription());
    dataSourceModel.setRelation(singleDataSourceUpdateReq.getDataSourceBase().getRelation());
    DataSourceExtensionModel dataSourceExtensionModel =
        getDataSourceExtensionModelById(dataSourceModel.getId());
    dataSourceExtensionModel.setIp(singleDataSourceUpdateReq.getIp());
    dataSourceExtensionModel.setPassword(singleDataSourceUpdateReq.getPassword());
    dataSourceExtensionModel.setPort(singleDataSourceUpdateReq.getPort());
    dataSourceExtensionModel.setDatabaseName(singleDataSourceUpdateReq.getDatabaseName());
    dataSourceExtensionModel.setJdbcParameter(singleDataSourceUpdateReq.getJdbcParameter());
    dataSourceExtensionModel.setUserName(singleDataSourceUpdateReq.getUserName());
    dataSourceRepository.save(dataSourceModel);
    dataSourceExtensionRepository.save(dataSourceExtensionModel);
  }

  @Override
  @Transactional
  public void updateMultipleDataSource(MultipleDataSourceUpdateReq multipleDataSourceUpdateReq) {
    DataSourceModel dataSourceModel = getDataSourceModelById(multipleDataSourceUpdateReq.getId());
    if (!dataSourceModel
        .getDatasourceName()
        .equals(multipleDataSourceUpdateReq.getDataSourceBase().getDatasourceName())) {
      checkDuplicateName(multipleDataSourceUpdateReq.getDataSourceBase().getDatasourceName());
    }
    dataSourceModel.setDatasourceName(
        multipleDataSourceUpdateReq.getDataSourceBase().getDatasourceName());
    dataSourceModel.setDatasourceDescription(
        multipleDataSourceUpdateReq.getDataSourceBase().getDatasourceDescription());
    dataSourceModel.setRelation(multipleDataSourceUpdateReq.getDataSourceBase().getRelation());
    dataSourceExtensionRepository.deleteAllByDatasourceId(multipleDataSourceUpdateReq.getId());
    dataSourceExtensionRepository.saveAll(
        buildDataSourceModelList(
            multipleDataSourceUpdateReq.getDataSourceExtensionBaseList(),
            multipleDataSourceUpdateReq.getId()));
  }

  @Override
  @Transactional
  public void deleteDataSource(Long id) {
//      需要检查此DB被多少个Agent使用了
    Long count = robotKBRepository.countByKbId(id);
    if(count > 0){
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.NOT_EMPTY, "此数据源被"+count+"个Agent使用了，请先解绑");

    }
    dataSourceRepository.deleteById(id);
    dataSourceExtensionRepository.deleteAllByDatasourceId(id);
  }

  @Override
  public PageResult<DataSourceSelectResp> selectDataSource(DataSourcePageReq dataSourcePageReq) {
    Pageable pageable =
        PageRequest.of(dataSourcePageReq.getPageNo() - 1, dataSourcePageReq.getPageSize());
    QDataSourceModel qDataSourceModel = QDataSourceModel.dataSourceModel;
    BooleanBuilder builder = new BooleanBuilder();
    builder.and(qDataSourceModel.tenantId.eq(LoginContextHolder.getLoginUserTenantId()));
    if (StrUtil.isNotBlank(dataSourcePageReq.getKeyWord())) {
      builder.and(
          qDataSourceModel
              .datasourceDescription
              .contains(dataSourcePageReq.getKeyWord())
              .or(qDataSourceModel.datasourceName.contains(dataSourcePageReq.getKeyWord())));
    }

    JPQLQuery<DataSourceSelectResp> query =
        queryFactory
            .select(
                Projections.bean(
                    DataSourceSelectResp.class,
                    qDataSourceModel.id,
                    qDataSourceModel.datasourceName,
                    qDataSourceModel.datasourceDescription,
                    qDataSourceModel.datasourceType))
            .from(qDataSourceModel)
            .where(builder)
            .orderBy(qDataSourceModel.updateTime.desc())
            .offset(pageable.getOffset())
            .limit(pageable.getPageSize());

    return PageResult.of(query.fetch(), query.fetchCount());
  }

  @Override
  public List<DataSourceSelectResp> listDataSource() {
    QDataSourceModel qDataSourceModel = QDataSourceModel.dataSourceModel;
    BooleanBuilder builder = new BooleanBuilder();
    builder.and(qDataSourceModel.tenantId.eq(LoginContextHolder.getLoginUserTenantId()));
    JPQLQuery jpqlQuery = queryFactory.select(Projections.bean(
            DataSourceSelectResp.class,
            qDataSourceModel.id,
            qDataSourceModel.datasourceName,
            qDataSourceModel.datasourceDescription,
            qDataSourceModel.datasourceType
    )).from(qDataSourceModel).where(builder).orderBy(qDataSourceModel.updateTime.desc());
    return jpqlQuery.fetch();
  }

  @Override
  public MultipleDataSourceResp getMultipleDataSourceById(Long id) {
    DataSourceModel dataSourceModel = getDataSourceModelById(id);
    DataSourceBase base = OrikaUtils.convert(dataSourceModel,DataSourceBase.class);
    List<DataSourceExtensionModel> extensionModelList = dataSourceExtensionRepository.getAllByDatasourceId(id);
    List<DataSourceExtensionBase> extensionBaseList = OrikaUtils.convertList(extensionModelList,DataSourceExtensionBase.class);
    MultipleDataSourceResp multipleDataSourceResp = new MultipleDataSourceResp();
    multipleDataSourceResp.setDataSourceBase(base);
    multipleDataSourceResp.setId(id);
    multipleDataSourceResp.setDataSourceExtensionBaseList(extensionBaseList);
    return multipleDataSourceResp;
  }

  @Override
  public SingleDataSourceResp getSingleDataSourceById(Long id) {
    DataSourceModel dataSourceModel = getDataSourceModelById(id);
    DataSourceExtensionModel extensionModel = dataSourceExtensionRepository.getByDatasourceId(id);
    SingleDataSourceResp singleDataSourceResp = OrikaUtils.convert(extensionModel,SingleDataSourceResp.class);
    DataSourceBase base = OrikaUtils.convert(dataSourceModel,DataSourceBase.class);
    singleDataSourceResp.setDataSourceBase(base);
    singleDataSourceResp.setId(id);
    return singleDataSourceResp;
  }

  @Override
  public List<DataSourceExtensionBase> listAllTables(DataBaseInfo dataBaseInfo) {
    DatabaseConnector mysql =
            DataConnectorFactory.getConnector(
                    DataSourceEnum.MYSQL, StrUtil.EMPTY, dataBaseInfo.getDatabaseName(), dataBaseInfo.getIp(), dataBaseInfo.getPort(),
                    dataBaseInfo.getUserName(), dataBaseInfo.getPassword(), dataBaseInfo.getJdbcParameter());
    List<DataSourceExtensionBase> extensionBaseList = new ArrayList<>();
    try{
      List<Map<String,String>> tableList = mysql.getTables(dataBaseInfo.getDatabaseName());
      Iterator<Map<String,String>> iterator = tableList.iterator();
      while (iterator.hasNext()){
        DataSourceExtensionBase extensionBase = OrikaUtils.convert(dataBaseInfo,DataSourceExtensionBase.class);
        Map<String,String> map = iterator.next();
        extensionBase.setTableName(map.get("fileName"));
        extensionBaseList.add(extensionBase);
      }
    }catch (Exception e){
      log.error("查询目标数据库中的表。",e);
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR, "系统正忙，请稍后再试！");
    }finally{
      mysql.close();
    }

    return extensionBaseList;
  }

  @Override
  public TableDataResp selectData(DataSourceExtensionBase dataSourceExtensionBase) {
    TableDataResp dataReps = new TableDataResp();
    ResultSet columnResultSet = null;
    List columnList = new ArrayList<>();
    List dataList = new ArrayList<>();
    DatabaseConnector connector = DataConnectorFactory.getConnector(DataSourceEnum.MYSQL,StrUtil.EMPTY, dataSourceExtensionBase.getDatabaseName(),
            dataSourceExtensionBase.getIp(), dataSourceExtensionBase.getPort(), dataSourceExtensionBase.getUserName(),
            dataSourceExtensionBase.getPassword(), dataSourceExtensionBase.getJdbcParameter());
    try (Connection connection = connector.getConnection()) {
      String sql = connector.getSql(dataSourceExtensionBase.getTableName());
      columnResultSet = connection.createStatement().executeQuery(sql);
      ResultSetMetaData metaData = columnResultSet.getMetaData();
      for (int i = 1; i <= metaData.getColumnCount(); i++) {
        columnList.add(metaData.getColumnName(i));
      }
      // 获取数据行
      while (columnResultSet.next()) {
        Map<String, Object> rowMap = new LinkedHashMap<>();
        for (int i = 1; i <= metaData.getColumnCount(); i++) {
          rowMap.put(metaData.getColumnName(i), columnResultSet.getObject(i));
        }
        dataList.add(rowMap);
      }
    } catch (SQLException e) {
      log.error("获取表数据出错!"+connector.toString()+",tableName="+dataSourceExtensionBase.getTableName(), e);
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.GET_OBJECT_ERROR, "获取表数据出错!");
    } finally {
      try {
        if (connector != null) {
          connector.close();
        }
      } catch (Exception e) {
        log.error("关闭数据库连接出错!", e);
        throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.CLOSE_DATABASE_ERROR);
      }
    }
    dataReps.setColumn(columnList);
    dataReps.setData(dataList);
    return dataReps;
  }

  @Override
  public String getTableDDL(DataSourceExtensionBase dataSourceExtensionBase) {
    DatabaseConnector connector = DataConnectorFactory.getConnector(DataSourceEnum.MYSQL,StrUtil.EMPTY, dataSourceExtensionBase.getDatabaseName(),
            dataSourceExtensionBase.getIp(), dataSourceExtensionBase.getPort(), dataSourceExtensionBase.getUserName(),
            dataSourceExtensionBase.getPassword(), dataSourceExtensionBase.getJdbcParameter());
    return connector.getTableDDL(connector,dataSourceExtensionBase.getTableName());
  }

  @Override
  public void checkConnection(DataBaseInfo dataBaseInfo) {
    checkConnection(
            DataSourceEnum.MYSQL,
            dataBaseInfo.getDatabaseName(),
            dataBaseInfo.getIp(),
            dataBaseInfo.getPort(),
            dataBaseInfo.getUserName(),
            dataBaseInfo.getPassword(),
            dataBaseInfo.getJdbcParameter());
  }

  private DataSourceModel getDataSourceModelById(Long id) {
    Optional<DataSourceModel> optional = dataSourceRepository.findById(id);
    if (!optional.isPresent()) {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "基础数据库信息不存在！");
    }
    return optional.get();
  }

  private DataSourceExtensionModel getDataSourceExtensionModelById(Long dataSourceId) {
    DataSourceExtensionModel dataSourceExtensionModel =
        dataSourceExtensionRepository.getByDatasourceId(dataSourceId);
    if (null == dataSourceExtensionModel) {
      throw ServiceExceptionUtil.exception(GlobalErrorCodeConstants.OBJECT_NOT_EXISTED, "数据库扩展信息不存在！");
    }
    return dataSourceExtensionModel;
  }

  private void checkDuplicateName(String dataSourceName) {
    if (null != dataSourceRepository.findByDatasourceName(dataSourceName)) {
      throw ServiceExceptionUtil.exception(
          GlobalErrorCodeConstants.DUPLICATED_OBJECT, "数据库名称已经存在！");
    }
  }

  private void checkConnection(
      DataSourceEnum categoryEnum,
      String databaseName,
      String host,
      Integer port,
      String userName,
      String password,
      String jdbcPara) {
    DatabaseConnector mysql =
        DataConnectorFactory.getConnector(
            categoryEnum, StrUtil.EMPTY, databaseName, host, port, userName, password, jdbcPara);
    mysql.checkConnection();
    mysql.close();
  }

  private DataSourceModel buildDataSourceMode(
      String dataSourceDescription,
      String dataSourceName,
      SingleAndMultipleEnum singleAndMultipleEnum,
      String relation) {
    DataSourceModel dataSourceModel = new DataSourceModel();
    dataSourceModel.setId(snowflake.nextId());
    dataSourceModel.setDatasourceType(SingleAndMultipleEnum.SINGLE);
    dataSourceModel.setDatasourceDescription(dataSourceDescription);
    dataSourceModel.setDatasourceName(dataSourceName);
    dataSourceModel.setDatasourceType(singleAndMultipleEnum);
    dataSourceModel.setRelation(relation);
    return dataSourceModel;
  }

  private DataSourceExtensionModel buildDataSourceExtensionModel(
      Long dataSourceModeId,
      String databaseName,
      String ip,
      String userName,
      String password,
      Integer port,
      String jdbcParameters,
      String tableName,
      String tableDescription) {
    DataSourceExtensionModel dataSourceExtensionModel = new DataSourceExtensionModel();
    dataSourceExtensionModel.setDatabaseName(databaseName);
    dataSourceExtensionModel.setIp(ip);
    dataSourceExtensionModel.setUserName(userName);
    dataSourceExtensionModel.setPort(port);
    dataSourceExtensionModel.setPassword(password);
    dataSourceExtensionModel.setJdbcParameter(jdbcParameters);
    dataSourceExtensionModel.setCategory(DataSourceEnum.MYSQL.getValue());
    dataSourceExtensionModel.setDatasourceId(dataSourceModeId);
    dataSourceExtensionModel.setTableName(tableName);
    dataSourceExtensionModel.setTableDescription(tableDescription);
    return dataSourceExtensionModel;
  }

  private List<DataSourceExtensionModel> buildDataSourceModelList(
      List<DataSourceExtensionBase> dataSourceExtensionBaseList, Long dataSourceId) {
    Iterator<DataSourceExtensionBase> it = dataSourceExtensionBaseList.listIterator();
    List<DataSourceExtensionModel> list = new ArrayList<>();
    while (it.hasNext()) {
      DataSourceExtensionBase dataSourceExtensionBase = it.next();
      DataSourceExtensionModel dataSourceExtensionModel =
          buildDataSourceExtensionModel(
              dataSourceId,
              dataSourceExtensionBase.getDatabaseName(),
              dataSourceExtensionBase.getIp(),
              dataSourceExtensionBase.getUserName(),
              dataSourceExtensionBase.getPassword(),
              dataSourceExtensionBase.getPort(),
              dataSourceExtensionBase.getJdbcParameter(),
              dataSourceExtensionBase.getTableName(),
              dataSourceExtensionBase.getTableDescription());
      list.add(dataSourceExtensionModel);
    }
    return list;
  }
}

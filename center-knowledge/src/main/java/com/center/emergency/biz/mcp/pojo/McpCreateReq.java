package com.center.emergency.biz.mcp.pojo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
public class McpCreateReq {

    @Schema(description = "MCP名称")
    @NotBlank(message = "MCP名称不能为空")
    @Length(min = 1, max = 50, message = "模型名称长度必须在1-50字符之间")
    private String mcpName;

    @Schema(description = "MCP描述")
    @Size(max = 500, message = "MCP描述长度不能超过500个字符")
    private String mcpDesc;

    @Schema(description = "安装方式",example = "SSE")
    @NotBlank(message = "安装方式不能为空")
    private String installMethod;

    @Schema(description = "服务名称")
    @NotBlank(message = "服务名称不能为空")
    @Pattern(regexp = "^[^\u4e00-\u9fa5]*$", message = "Server Name不能包含中文字符")
    private String serverName;

    @Schema(description = "服务URL")
    @NotBlank(message = "服务URL不能为空")
    private String serverUrl;

    @Schema(description = "Logo文件的ID")
    private Long logoId;
}


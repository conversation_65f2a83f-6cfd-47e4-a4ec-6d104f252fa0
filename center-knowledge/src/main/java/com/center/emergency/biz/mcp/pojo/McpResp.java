package com.center.emergency.biz.mcp.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class McpResp {
    @Schema(description = "Mcp主键ID")
    private Long id;

    @Schema(description = "MCP名称")
    private String mcpName;

    @Schema(description = "MCP描述")
    private String mcpDesc;

    @Schema(description = "安装方式")
    private String installMethod;

    @Schema(description = "服务名称")
    private String serverName;

    @Schema(description = "服务URL")
    private String serverUrl;

    @Schema(description = "LogoId")
    private Long logoId;

    @JsonIgnore
    @Schema(description = "HdfsPath")
    private String hdfsPath;

    @Schema(description = "Logo的预览地址")
    private String logoUrl;
}


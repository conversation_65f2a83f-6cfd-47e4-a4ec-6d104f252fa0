package com.center.emergency.common.utils;

import org.apache.commons.io.FilenameUtils;
import java.util.Arrays;
import java.util.List;

/**
 * 文件类型验证器类
 * 用于检查文件类型是否属于一组预定义的支持类型
 */
public class FileTypeValidator {

    // 支持的文件类型列表
    private static final List<String> SUPPORTED_TYPES = Arrays.asList(
            "md", "txt", "pdf", "jpg", "png", "jpeg", "docx", "xlsx", "pptx", "eml", "csv", "xls"
    );

    /**
     * 校验文件类型是否被支持
     *
     * @param filename 文件名，用于提取文件扩展名
     * @return 如果文件类型在支持的类型列表中，则返回true；否则返回false
     */
    public static boolean isSupportedFileType(String filename) {
        // 获取文件扩展名，并转换为小写，以确保与支持类型列表中的格式一致
        String fileExtension = FilenameUtils.getExtension(filename).toLowerCase();
        // 检查文件扩展名是否在支持的类型列表中
        return SUPPORTED_TYPES.contains(fileExtension);
    }
}
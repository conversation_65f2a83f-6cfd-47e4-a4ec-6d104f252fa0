package com.center.emergency.common.utils;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sts.v20180813.StsClient;
import com.tencentcloudapi.sts.v20180813.models.GetFederationTokenRequest;
import com.tencentcloudapi.sts.v20180813.models.GetFederationTokenResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TencentUtils {
    public static String GetTmpCredentials(String actionName) {
        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
//            江大
//            Credential cred = new Credential("AKIDWcbTL7g1895oYO6bsxpTs42mIcjeMp7B", "yhEYdV8XieMq591h9ORTItGPK0RedNvG");
//            中心
            Credential cred = new Credential("AKIDYyCGG2auzqSqztY1ECwRgxDItJUR9dss", "rTs2ye5HF7lBMJ1UiiT0OZrPnxtdBWfc");
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("sts.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            StsClient client = new StsClient(cred, "ap-guangzhou", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            GetFederationTokenRequest req = new GetFederationTokenRequest();
            req.setName("test");
            req.setPolicy("{\"version\":\"2.0\",\"statement\":[{\"effect\":\"allow\",\"action\":[\"name/"+actionName+":*\"],\"resource\":\"*\"}]}");
            req.setDurationSeconds(7200L);
            // 返回的resp是一个GetFederationTokenResponse的实例，与请求对象对应
            GetFederationTokenResponse getFederationTokenResponse = client.GetFederationToken(req);
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(getFederationTokenResponse.getCredentials().getTmpSecretId());
            stringBuilder.append("&&&&&");
            stringBuilder.append(getFederationTokenResponse.getCredentials().getTmpSecretKey());
            stringBuilder.append("&&&&&");
            stringBuilder.append(getFederationTokenResponse.getCredentials().getToken());
            return stringBuilder.toString();
        } catch (TencentCloudSDKException e) {
            log.error("生成语音服务临时证书出错！",e);
        }
        return null;
    }
}
